from dataclasses import dataclass
from typing import Optional

@dataclass
class MessagePayload:
    content: str
    encrypted: bool = False
    extra: Optional[dict] = None

    def to_dict(self):
        return {
            'content': self.content,
            'encrypted': self.encrypted,
            'extra': self.extra
        }

    @staticmethod
    def from_dict(data):
        return MessagePayload(
            content=data['content'],
            encrypted=data.get('encrypted', False),
            extra=data.get('extra')
        ) 