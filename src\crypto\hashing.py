"""
哈希和消息认证码模块
提供安全的哈希函数、HMAC和密钥派生功能
"""

import hashlib
import hmac
import secrets
from typing import Optional, Union
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
import base64
import time


class SecureHash:
    """安全哈希类"""
    
    @staticmethod
    def sha256(data: Union[str, bytes]) -> bytes:
        """SHA-256哈希"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.sha256(data).digest()
    
    @staticmethod
    def sha512(data: Union[str, bytes]) -> bytes:
        """SHA-512哈希"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.sha512(data).digest()
    
    @staticmethod
    def blake2b(data: Union[str, bytes], key: Optional[bytes] = None) -> bytes:
        """BLAKE2b哈希（可选密钥）"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.blake2b(data, key=key).digest()
    
    @staticmethod
    def sha256_hex(data: Union[str, bytes]) -> str:
        """SHA-256哈希（十六进制字符串）"""
        return SecureHash.sha256(data).hex()
    
    @staticmethod
    def sha256_base64(data: Union[str, bytes]) -> str:
        """SHA-256哈希（Base64字符串）"""
        return base64.b64encode(SecureHash.sha256(data)).decode('ascii')


class MessageAuth:
    """消息认证码类"""
    
    def __init__(self, key: Optional[bytes] = None):
        self.key = key or secrets.token_bytes(32)
    
    def generate_key(self) -> bytes:
        """生成新的HMAC密钥"""
        self.key = secrets.token_bytes(32)
        return self.key
    
    def hmac_sha256(self, message: Union[str, bytes], key: Optional[bytes] = None) -> bytes:
        """HMAC-SHA256"""
        if isinstance(message, str):
            message = message.encode('utf-8')
        
        auth_key = key or self.key
        if not auth_key:
            raise ValueError("需要提供HMAC密钥")
        
        return hmac.new(auth_key, message, hashlib.sha256).digest()
    
    def hmac_sha512(self, message: Union[str, bytes], key: Optional[bytes] = None) -> bytes:
        """HMAC-SHA512"""
        if isinstance(message, str):
            message = message.encode('utf-8')
        
        auth_key = key or self.key
        if not auth_key:
            raise ValueError("需要提供HMAC密钥")
        
        return hmac.new(auth_key, message, hashlib.sha512).digest()
    
    def verify_hmac(self, message: Union[str, bytes], mac: bytes, key: Optional[bytes] = None) -> bool:
        """验证HMAC"""
        try:
            expected_mac = self.hmac_sha256(message, key)
            return hmac.compare_digest(mac, expected_mac)
        except Exception:
            return False
    
    def create_authenticated_message(self, message: Union[str, bytes], key: Optional[bytes] = None) -> dict:
        """创建带认证码的消息"""
        if isinstance(message, str):
            message_bytes = message.encode('utf-8')
        else:
            message_bytes = message
        
        mac = self.hmac_sha256(message_bytes, key)
        timestamp = int(time.time())
        
        return {
            'message': base64.b64encode(message_bytes).decode('ascii'),
            'mac': base64.b64encode(mac).decode('ascii'),
            'timestamp': timestamp
        }
    
    def verify_authenticated_message(self, auth_message: dict, key: Optional[bytes] = None, 
                                   max_age: int = 300) -> Optional[bytes]:
        """验证带认证码的消息"""
        try:
            # 检查时间戳
            current_time = int(time.time())
            if current_time - auth_message['timestamp'] > max_age:
                return None  # 消息过期
            
            # 解码消息和MAC
            message = base64.b64decode(auth_message['message'])
            mac = base64.b64decode(auth_message['mac'])
            
            # 验证MAC
            if self.verify_hmac(message, mac, key):
                return message
            return None
        except Exception:
            return None


class KeyDerivation:
    """密钥派生类"""
    
    @staticmethod
    def pbkdf2(password: Union[str, bytes], salt: Optional[bytes] = None, 
               iterations: int = 100000, key_length: int = 32) -> tuple[bytes, bytes]:
        """PBKDF2密钥派生"""
        if isinstance(password, str):
            password = password.encode('utf-8')
        
        if salt is None:
            salt = secrets.token_bytes(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=key_length,
            salt=salt,
            iterations=iterations,
        )
        
        key = kdf.derive(password)
        return key, salt
    
    @staticmethod
    def scrypt(password: Union[str, bytes], salt: Optional[bytes] = None,
               n: int = 2**14, r: int = 8, p: int = 1, key_length: int = 32) -> tuple[bytes, bytes]:
        """Scrypt密钥派生（更安全但更慢）"""
        if isinstance(password, str):
            password = password.encode('utf-8')
        
        if salt is None:
            salt = secrets.token_bytes(16)
        
        kdf = Scrypt(
            algorithm=hashes.SHA256(),
            length=key_length,
            salt=salt,
            n=n,
            r=r,
            p=p,
        )
        
        key = kdf.derive(password)
        return key, salt
    
    @staticmethod
    def hkdf_expand(input_key: bytes, info: bytes = b"", length: int = 32) -> bytes:
        """HKDF密钥扩展"""
        hkdf = HKDF(
            algorithm=hashes.SHA256(),
            length=length,
            salt=None,
            info=info,
        )
        return hkdf.derive(input_key)
    
    @staticmethod
    def derive_multiple_keys(master_key: bytes, count: int, key_length: int = 32) -> list[bytes]:
        """从主密钥派生多个子密钥"""
        keys = []
        for i in range(count):
            info = f"subkey_{i}".encode()
            key = KeyDerivation.hkdf_expand(master_key, info, key_length)
            keys.append(key)
        return keys


class IntegrityChecker:
    """完整性检查器"""
    
    def __init__(self):
        self.hash_func = SecureHash()
        self.auth = MessageAuth()
    
    def create_checksum(self, data: bytes) -> dict:
        """创建数据校验和"""
        return {
            'sha256': self.hash_func.sha256_hex(data),
            'size': len(data),
            'timestamp': int(time.time())
        }
    
    def verify_checksum(self, data: bytes, checksum: dict) -> bool:
        """验证数据校验和"""
        try:
            # 检查大小
            if len(data) != checksum['size']:
                return False
            
            # 检查哈希
            expected_hash = self.hash_func.sha256_hex(data)
            return expected_hash == checksum['sha256']
        except Exception:
            return False
    
    def create_secure_checksum(self, data: bytes, key: bytes) -> dict:
        """创建安全校验和（包含HMAC）"""
        basic_checksum = self.create_checksum(data)
        mac = self.auth.hmac_sha256(data, key)
        
        basic_checksum['hmac'] = base64.b64encode(mac).decode('ascii')
        return basic_checksum
    
    def verify_secure_checksum(self, data: bytes, checksum: dict, key: bytes) -> bool:
        """验证安全校验和"""
        try:
            # 基本校验和验证
            if not self.verify_checksum(data, checksum):
                return False
            
            # HMAC验证
            if 'hmac' in checksum:
                expected_mac = self.auth.hmac_sha256(data, key)
                provided_mac = base64.b64decode(checksum['hmac'])
                return hmac.compare_digest(expected_mac, provided_mac)
            
            return True
        except Exception:
            return False


# 示例使用
if __name__ == "__main__":
    print("=== 哈希函数示例 ===")
    data = "Hello, World!"
    print(f"原始数据: {data}")
    print(f"SHA-256: {SecureHash.sha256_hex(data)}")
    print(f"SHA-512: {SecureHash.sha512(data).hex()}")
    
    print("\n=== HMAC示例 ===")
    auth = MessageAuth()
    key = auth.generate_key()
    print(f"HMAC密钥: {base64.b64encode(key).decode()}")
    
    mac = auth.hmac_sha256(data)
    print(f"HMAC: {base64.b64encode(mac).decode()}")
    print(f"验证结果: {auth.verify_hmac(data, mac)}")
    
    # 带认证的消息
    auth_msg = auth.create_authenticated_message(data)
    print(f"认证消息: {auth_msg}")
    
    verified_msg = auth.verify_authenticated_message(auth_msg)
    print(f"验证消息: {verified_msg.decode() if verified_msg else 'None'}")
    
    print("\n=== 密钥派生示例 ===")
    password = "my_secure_password"
    key, salt = KeyDerivation.pbkdf2(password)
    print(f"派生密钥: {base64.b64encode(key).decode()}")
    print(f"盐值: {base64.b64encode(salt).decode()}")
    
    # 派生多个子密钥
    master_key = secrets.token_bytes(32)
    sub_keys = KeyDerivation.derive_multiple_keys(master_key, 3)
    for i, sub_key in enumerate(sub_keys):
        print(f"子密钥 {i+1}: {base64.b64encode(sub_key).decode()[:32]}...")
    
    print("\n=== 完整性检查示例 ===")
    checker = IntegrityChecker()
    test_data = b"This is test data for integrity checking"
    
    # 基本校验和
    checksum = checker.create_checksum(test_data)
    print(f"校验和: {checksum}")
    print(f"验证结果: {checker.verify_checksum(test_data, checksum)}")
    
    # 安全校验和
    secure_key = secrets.token_bytes(32)
    secure_checksum = checker.create_secure_checksum(test_data, secure_key)
    print(f"安全校验和: {secure_checksum}")
    print(f"安全验证结果: {checker.verify_secure_checksum(test_data, secure_checksum, secure_key)}")
