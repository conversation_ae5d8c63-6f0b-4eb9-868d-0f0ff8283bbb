"""
匿名加密通讯系统 - 工具模块
提供配置管理、日志记录和辅助函数
"""

from .config import Config<PERSON><PERSON><PERSON>, config_manager, get_client_config, get_server_config
from .logger import SecurityLogger, AuditLogger, PerformanceLogger, security_logger, audit_logger, performance_logger, setup_logging
from .helpers import (
    generate_unique_id, format_bytes, format_duration, format_timestamp,
    safe_json_loads, safe_json_dumps, calculate_file_hash, verify_file_integrity,
    secure_random_string, encode_base64, decode_base64, sanitize_filename,
    validate_ip_address, validate_port, validate_url, get_system_info,
    retry_on_failure, timeout_handler, chunk_data, merge_chunks,
    calculate_checksum, verify_checksum, create_directory_if_not_exists,
    is_file_locked, get_file_size, get_file_modification_time,
    is_port_available, find_available_port, parse_connection_string
)

__all__ = [
    # 配置管理
    'ConfigManager',
    'config_manager',
    'get_client_config',
    'get_server_config',
    
    # 日志记录
    'SecurityLogger',
    'AuditLogger', 
    'PerformanceLogger',
    'security_logger',
    'audit_logger',
    'performance_logger',
    'setup_logging',
    
    # 辅助函数
    'generate_unique_id',
    'format_bytes',
    'format_duration', 
    'format_timestamp',
    'safe_json_loads',
    'safe_json_dumps',
    'calculate_file_hash',
    'verify_file_integrity',
    'secure_random_string',
    'encode_base64',
    'decode_base64',
    'sanitize_filename',
    'validate_ip_address',
    'validate_port',
    'validate_url',
    'get_system_info',
    'retry_on_failure',
    'timeout_handler',
    'chunk_data',
    'merge_chunks',
    'calculate_checksum',
    'verify_checksum',
    'create_directory_if_not_exists',
    'is_file_locked',
    'get_file_size',
    'get_file_modification_time',
    'is_port_available',
    'find_available_port',
    'parse_connection_string'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Anonymous Crypto Chat Team"
__description__ = "Utility modules for anonymous encrypted communication system"
