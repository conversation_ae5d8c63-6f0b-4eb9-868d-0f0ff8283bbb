# 匿名加密通讯系统 - 协议消息系统

## 概述

本系统实现了完整的端到端加密匿名通信，使用 `SecureProtocol` 和 `KeyExchange` 协议消息系统，确保通信的安全性和匿名性。

## 核心组件

### 1. SecureProtocol
- **功能**: 安全协议实现，提供端到端加密和消息处理
- **特性**: 
  - 握手初始化、响应、完成
  - 文本消息加密解密
  - 文件传输加密
  - 心跳消息
  - 会话管理

### 2. KeyExchange
- **功能**: 密钥交换实现，提供RSA密钥交换和会话密钥管理
- **特性**:
  - RSA密钥对生成
  - 会话密钥交换
  - 会话密钥存储和管理

### 3. Message
- **功能**: 消息协议定义，包含消息类型、消息头和消息体
- **消息类型**:
  - HANDSHAKE_INIT: 握手初始化
  - HANDSHAKE_RESPONSE: 握手响应
  - HANDSHAKE_COMPLETE: 握手完成
  - TEXT_MESSAGE: 文本消息
  - FILE_TRANSFER_INIT: 文件传输初始化
  - FILE_CHUNK: 文件块
  - HEARTBEAT: 心跳消息
  - ERROR: 错误消息

## 快速开始

### 1. 测试协议消息系统
```bash
# 测试协议消息系统
python run_system.py test-protocol

# 或者直接运行测试脚本
python test_protocol_system.py
```

### 2. 启动中继服务器
```bash
# 启动单个中继服务器
python run_system.py relay 8001

# 启动中继网络（多个服务器）
python run_system.py network 8001,8002,8003
```

### 3. 启动客户端
```bash
# 启动客户端（Alice）
python run_system.py client Alice ws://localhost:8001

# 启动客户端（Bob）
python run_system.py client Bob ws://localhost:8001
```

### 4. 测试完整系统
```bash
# 测试完整系统（自动启动服务器和客户端）
python run_system.py test-full
```

## 通信流程

### 1. 密钥交换流程
```
Alice                    Bob
  |                       |
  |-- HANDSHAKE_INIT ---->|  (Alice发送公钥)
  |                       |
  |<-- HANDSHAKE_RESPONSE-|  (Bob发送公钥和加密的会话密钥)
  |                       |
  |-- HANDSHAKE_COMPLETE->|  (Alice确认握手完成)
  |                       |
```

### 2. 消息加密流程
```
Alice                    Bob
  |                       |
  |-- TEXT_MESSAGE ------>|  (Alice用会话密钥加密消息)
  |                       |
  |<-- TEXT_MESSAGE ------|  (Bob用会话密钥解密消息)
  |                       |
```

## 安全特性

### 1. 端到端加密
- 使用RSA-2048进行密钥交换
- 使用AES-GCM进行消息加密
- 会话密钥定期更新

### 2. 匿名性
- 中继服务器无法解密消息内容
- 支持洋葱路由（多跳转发）
- 流量混淆和虚假流量

### 3. 完整性保护
- 消息签名验证
- 防重放攻击
- 会话超时管理

## 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client A  │    │   Client B  │    │   Client C  │
│             │    │             │    │             │
│ SecureProtocol│    │ SecureProtocol│    │ SecureProtocol│
│ KeyExchange   │    │ KeyExchange   │    │ KeyExchange   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
              ┌─────────────────────────┐
              │     Relay Network       │
              │                         │
              │  ┌─────┐ ┌─────┐ ┌─────┐ │
              │  │Relay│ │Relay│ │Relay│ │
              │  │ 1   │ │ 2   │ │ 3   │ │
              │  └─────┘ └─────┘ └─────┘ │
              └─────────────────────────┘
```

## 配置说明

### 客户端配置
```json
{
  "user_id": "Alice",
  "relay_servers": [
    "ws://localhost:8001",
    "ws://localhost:8002",
    "ws://localhost:8003"
  ],
  "auto_connect": true,
  "identity_rotation_interval": 3600,
  "circuit_length": 3,
  "use_dummy_traffic": true
}
```

### 服务器配置
```json
{
  "host": "localhost",
  "port": 8001,
  "max_connections": 1000,
  "connection_timeout": 300,
  "log_level": "INFO"
}
```

## 故障排除

### 1. 连接问题
- 检查中继服务器是否启动
- 确认端口是否被占用
- 检查防火墙设置

### 2. 加密问题
- 确认cryptography库已安装
- 检查密钥生成是否成功
- 验证会话密钥是否正确

### 3. 消息传递问题
- 检查用户ID是否正确
- 确认联系人是否已添加
- 验证会话是否已建立

## 开发指南

### 1. 添加新的消息类型
```python
# 在 MessageType 枚举中添加
class MessageType(Enum):
    NEW_MESSAGE_TYPE = "new_message_type"

# 在 Message 类中添加创建方法
@classmethod
def create_new_message(cls, sender_id: str, target_id: str, content: str) -> 'Message':
    header = MessageHeader(
        message_type=MessageType.NEW_MESSAGE_TYPE,
        sender_id=sender_id,
        target_id=target_id,
        timestamp=int(time.time())
    )
    payload = MessagePayload(content=content, encrypted=True)
    return cls(header, payload)
```

### 2. 扩展加密算法
```python
# 在 SecureProtocol 中添加新的加密方法
def encrypt_with_new_algorithm(self, data: bytes, key: bytes) -> bytes:
    # 实现新的加密算法
    pass

def decrypt_with_new_algorithm(self, encrypted_data: bytes, key: bytes) -> bytes:
    # 实现新的解密算法
    pass
```

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 联系方式

如有问题，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者 