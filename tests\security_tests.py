"""
安全测试模块
对匿名加密通讯系统进行全面的安全测试
"""

import unittest
import asyncio
import time
import secrets
import threading
from typing import List, Dict
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.crypto import (
    SymmetricCrypto, RSACrypto, ECCCrypto, ECDHKeyExchange,
    SecureHash, MessageAuth, KeyDerivation, KeyManager
)
from src.network import OnionRouter, TrafficObfuscator, AnonymousIdentityManager


class CryptoSecurityTests(unittest.TestCase):
    """加密安全测试"""
    
    def setUp(self):
        self.symmetric = SymmetricCrypto()
        self.rsa = RSACrypto()
        self.ecc = ECCCrypto()
        self.ecdh = ECDHKeyExchange()
        self.hash = SecureHash()
        self.auth = MessageAuth()
    
    def test_symmetric_encryption_strength(self):
        """测试对称加密强度"""
        print("测试对称加密强度...")
        
        # 测试密钥长度
        key = self.symmetric.generate_key()
        self.assertEqual(len(key), 32, "AES-256密钥长度应为32字节")
        
        # 测试加密随机性
        message = "测试消息"
        key = self.symmetric.generate_key()
        
        encrypted_messages = []
        for _ in range(100):
            encrypted = self.symmetric.encrypt_message(message, key)
            encrypted_messages.append(encrypted)
        
        # 确保每次加密结果都不同（由于随机nonce）
        unique_encryptions = set(encrypted_messages)
        self.assertEqual(len(unique_encryptions), 100, "每次加密应产生不同结果")
        
        # 测试解密正确性
        for encrypted in encrypted_messages:
            decrypted = self.symmetric.decrypt_message(encrypted, key)
            self.assertEqual(decrypted, message, "解密结果应与原文一致")
    
    def test_asymmetric_key_strength(self):
        """测试非对称密钥强度"""
        print("测试非对称密钥强度...")
        
        # 测试RSA密钥长度
        rsa_private, rsa_public = self.rsa.generate_keypair()
        self.assertEqual(rsa_private.key_size, 4096, "RSA密钥长度应为4096位")
        
        # 测试ECC密钥
        ecc_private, ecc_public = self.ecc.generate_keypair()
        self.assertIsNotNone(ecc_private, "ECC私钥应成功生成")
        self.assertIsNotNone(ecc_public, "ECC公钥应成功生成")
        
        # 测试数字签名
        message = b"测试签名消息"
        
        # RSA签名
        rsa_signature = self.rsa.sign(message, rsa_private)
        self.assertTrue(self.rsa.verify(message, rsa_signature, rsa_public), "RSA签名验证应成功")
        
        # 篡改消息后验证应失败
        tampered_message = b"篡改的消息"
        self.assertFalse(self.rsa.verify(tampered_message, rsa_signature, rsa_public), "篡改消息的签名验证应失败")
        
        # ECC签名
        ecc_signature = self.ecc.sign(message, ecc_private)
        self.assertTrue(self.ecc.verify(message, ecc_signature, ecc_public), "ECC签名验证应成功")
        self.assertFalse(self.ecc.verify(tampered_message, ecc_signature, ecc_public), "篡改消息的ECC签名验证应失败")
    
    def test_key_exchange_security(self):
        """测试密钥交换安全性"""
        print("测试密钥交换安全性...")
        
        # 生成两对密钥
        alice_private, alice_public = self.ecdh.generate_keypair()
        bob_private, bob_public = self.ecdh.generate_keypair()
        
        # 执行密钥交换
        alice_shared = self.ecdh.perform_exchange(alice_private, bob_public)
        bob_shared = self.ecdh.perform_exchange(bob_private, alice_public)
        
        # 验证共享密钥相同
        self.assertEqual(alice_shared, bob_shared, "双方应得到相同的共享密钥")
        
        # 测试密钥唯一性
        shared_keys = set()
        for _ in range(10):
            priv1, pub1 = self.ecdh.generate_keypair()
            priv2, pub2 = self.ecdh.generate_keypair()
            shared = self.ecdh.perform_exchange(priv1, pub2)
            shared_keys.add(shared)
        
        self.assertEqual(len(shared_keys), 10, "不同密钥对应产生不同的共享密钥")
    
    def test_hash_collision_resistance(self):
        """测试哈希碰撞抗性"""
        print("测试哈希碰撞抗性...")
        
        # 生成大量不同输入的哈希值
        hashes = set()
        for i in range(10000):
            data = f"测试数据{i}_{secrets.token_hex(16)}"
            hash_value = self.hash.sha256(data)
            hashes.add(hash_value)
        
        # 确保没有碰撞
        self.assertEqual(len(hashes), 10000, "不应出现哈希碰撞")
        
        # 测试相同输入产生相同哈希
        test_data = "固定测试数据"
        hash1 = self.hash.sha256(test_data)
        hash2 = self.hash.sha256(test_data)
        self.assertEqual(hash1, hash2, "相同输入应产生相同哈希")
    
    def test_hmac_security(self):
        """测试HMAC安全性"""
        print("测试HMAC安全性...")
        
        key = self.auth.generate_key()
        message = "测试消息"
        
        # 生成HMAC
        mac = self.auth.hmac_sha256(message, key)
        
        # 验证正确性
        self.assertTrue(self.auth.verify_hmac(message, mac, key), "HMAC验证应成功")
        
        # 测试密钥敏感性
        wrong_key = secrets.token_bytes(32)
        self.assertFalse(self.auth.verify_hmac(message, mac, wrong_key), "错误密钥的HMAC验证应失败")
        
        # 测试消息完整性
        tampered_message = "篡改的消息"
        self.assertFalse(self.auth.verify_hmac(tampered_message, mac, key), "篡改消息的HMAC验证应失败")
    
    def test_key_derivation_security(self):
        """测试密钥派生安全性"""
        print("测试密钥派生安全性...")
        
        password = "测试密码"
        
        # 测试PBKDF2
        key1, salt1 = KeyDerivation.pbkdf2(password)
        key2, salt2 = KeyDerivation.pbkdf2(password)
        
        # 不同盐值应产生不同密钥
        self.assertNotEqual(salt1, salt2, "应使用不同的盐值")
        self.assertNotEqual(key1, key2, "不同盐值应产生不同密钥")
        
        # 相同密码和盐值应产生相同密钥
        key3, _ = KeyDerivation.pbkdf2(password, salt1)
        self.assertEqual(key1, key3, "相同密码和盐值应产生相同密钥")
        
        # 测试Scrypt
        scrypt_key1, scrypt_salt1 = KeyDerivation.scrypt(password)
        scrypt_key2, scrypt_salt2 = KeyDerivation.scrypt(password)
        
        self.assertNotEqual(scrypt_salt1, scrypt_salt2, "Scrypt应使用不同盐值")
        self.assertNotEqual(scrypt_key1, scrypt_key2, "Scrypt不同盐值应产生不同密钥")


class AnonymityTests(unittest.TestCase):
    """匿名性测试"""
    
    def setUp(self):
        self.onion_router = OnionRouter()
        self.obfuscator = TrafficObfuscator()
        self.identity_manager = AnonymousIdentityManager()
    
    def test_identity_rotation(self):
        """测试身份轮换"""
        print("测试身份轮换...")
        
        # 创建初始身份
        identity1 = self.identity_manager.create_identity()
        self.identity_manager.switch_identity(identity1)
        
        # 轮换身份
        identity2 = self.identity_manager.rotate_identity()
        
        # 验证身份不同
        self.assertNotEqual(identity1, identity2, "轮换后应得到不同身份")
        
        # 验证当前身份已更新
        current = self.identity_manager.get_current_identity()
        self.assertEqual(current['identity_id'], identity2, "当前身份应为新身份")
    
    def test_traffic_obfuscation(self):
        """测试流量混淆"""
        print("测试流量混淆...")
        
        # 测试数据包填充
        original_data = b"测试数据"
        padded_data = self.obfuscator.pad_packet(original_data)
        
        # 验证填充后大小在预期范围内
        self.assertGreaterEqual(len(padded_data), self.obfuscator.min_packet_size, "填充后大小应不小于最小值")
        self.assertLessEqual(len(padded_data), self.obfuscator.max_packet_size, "填充后大小应不大于最大值")
        
        # 测试去填充
        unpadded_data = self.obfuscator.unpad_packet(padded_data)
        self.assertEqual(unpadded_data, original_data, "去填充后应恢复原始数据")
        
        # 测试虚假流量生成
        dummy_traffic = self.obfuscator.generate_dummy_traffic()
        self.assertTrue(self.obfuscator.is_dummy_traffic(dummy_traffic), "应正确识别虚假流量")
        
        # 测试真实流量不被误识别
        real_traffic = b"真实流量数据"
        self.assertFalse(self.obfuscator.is_dummy_traffic(real_traffic), "真实流量不应被误识别为虚假流量")
    
    def test_circuit_diversity(self):
        """测试电路多样性"""
        print("测试电路多样性...")
        
        # 添加测试节点
        from src.network.anonymizer import RelayNode
        nodes = [
            RelayNode(f"node_{i}", "127.0.0.1", 8000+i, b"key") 
            for i in range(10)
        ]
        
        for node in nodes:
            self.onion_router.add_relay_node(node)
        
        # 生成多条电路路径
        paths = []
        for _ in range(100):
            try:
                path = self.onion_router.select_circuit_path(3)
                paths.append(tuple(path))
            except ValueError:
                break  # 节点不足
        
        # 验证路径多样性
        unique_paths = set(paths)
        diversity_ratio = len(unique_paths) / len(paths)
        self.assertGreater(diversity_ratio, 0.8, "电路路径应具有足够的多样性")


class NetworkSecurityTests(unittest.TestCase):
    """网络安全测试"""
    
    def test_replay_attack_protection(self):
        """测试重放攻击防护"""
        print("测试重放攻击防护...")
        
        from src.network.protocol import SecureProtocol
        
        protocol = SecureProtocol("TestUser")
        
        # 创建消息
        message = protocol.create_handshake_init("PeerUser", b"test_key")
        
        # 序列化消息
        serialized = protocol.serialize_message(message)
        
        # 模拟重放攻击（相同序列号）
        replayed_message = protocol.deserialize_message(serialized)
        
        # 验证序列号检查
        self.assertEqual(message.header.sequence_number, replayed_message.header.sequence_number)
        
        # 在实际实现中，应该有序列号验证机制防止重放
    
    def test_timing_attack_resistance(self):
        """测试时序攻击抗性"""
        print("测试时序攻击抗性...")
        
        import hmac
        
        # 测试常数时间比较
        key = secrets.token_bytes(32)
        message = b"测试消息"
        
        correct_mac = hmac.new(key, message, 'sha256').digest()
        wrong_mac = secrets.token_bytes(32)
        
        # 测试比较时间
        times_correct = []
        times_wrong = []
        
        for _ in range(1000):
            start = time.perf_counter()
            hmac.compare_digest(correct_mac, correct_mac)
            times_correct.append(time.perf_counter() - start)
            
            start = time.perf_counter()
            hmac.compare_digest(correct_mac, wrong_mac)
            times_wrong.append(time.perf_counter() - start)
        
        # 验证时间差异不显著（常数时间比较）
        avg_correct = sum(times_correct) / len(times_correct)
        avg_wrong = sum(times_wrong) / len(times_wrong)
        
        # 时间差异应该很小
        time_diff_ratio = abs(avg_correct - avg_wrong) / max(avg_correct, avg_wrong)
        self.assertLess(time_diff_ratio, 0.1, "常数时间比较的时间差异应很小")


class PerformanceTests(unittest.TestCase):
    """性能测试"""
    
    def test_encryption_performance(self):
        """测试加密性能"""
        print("测试加密性能...")
        
        symmetric = SymmetricCrypto()
        key = symmetric.generate_key()
        
        # 测试小消息加密性能
        small_message = "小消息" * 10
        start_time = time.time()
        
        for _ in range(1000):
            encrypted = symmetric.encrypt_message(small_message, key)
            decrypted = symmetric.decrypt_message(encrypted, key)
            self.assertEqual(decrypted, small_message)
        
        small_msg_time = time.time() - start_time
        print(f"小消息加密性能: {1000/small_msg_time:.2f} 消息/秒")
        
        # 测试大消息加密性能
        large_message = "大消息" * 1000
        start_time = time.time()
        
        for _ in range(100):
            encrypted = symmetric.encrypt_message(large_message, key)
            decrypted = symmetric.decrypt_message(encrypted, key)
            self.assertEqual(decrypted, large_message)
        
        large_msg_time = time.time() - start_time
        print(f"大消息加密性能: {100/large_msg_time:.2f} 消息/秒")
        
        # 性能应该在合理范围内
        self.assertLess(small_msg_time, 10, "小消息加密应在10秒内完成1000次")
        self.assertLess(large_msg_time, 30, "大消息加密应在30秒内完成100次")
    
    def test_key_generation_performance(self):
        """测试密钥生成性能"""
        print("测试密钥生成性能...")
        
        # 测试对称密钥生成
        symmetric = SymmetricCrypto()
        start_time = time.time()
        
        for _ in range(1000):
            key = symmetric.generate_key()
            self.assertEqual(len(key), 32)
        
        symmetric_time = time.time() - start_time
        print(f"对称密钥生成性能: {1000/symmetric_time:.2f} 密钥/秒")
        
        # 测试非对称密钥生成
        rsa = RSACrypto()
        start_time = time.time()
        
        for _ in range(5):  # RSA密钥生成较慢
            private_key, public_key = rsa.generate_keypair()
            self.assertIsNotNone(private_key)
            self.assertIsNotNone(public_key)
        
        rsa_time = time.time() - start_time
        print(f"RSA密钥生成性能: {5/rsa_time:.2f} 密钥对/秒")
        
        # 性能检查
        self.assertLess(symmetric_time, 5, "对称密钥生成应很快")
        self.assertLess(rsa_time, 60, "RSA密钥生成应在合理时间内")


def run_security_tests():
    """运行所有安全测试"""
    print("=== 开始安全测试 ===\n")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        CryptoSecurityTests,
        AnonymityTests,
        NetworkSecurityTests,
        PerformanceTests
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n=== 测试完成 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_security_tests()
    sys.exit(0 if success else 1)
