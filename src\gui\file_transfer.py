"""
文件传输组件 - 处理文件发送和接收的界面
提供文件选择、传输进度显示和管理功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime


class FileTransferWidget:
    """文件传输组件"""
    
    def __init__(self, parent, on_send_file: Callable[[str, str], None] = None):
        self.parent = parent
        self.on_send_file = on_send_file
        
        # 传输数据
        self.transfers: Dict[str, Dict] = {}  # transfer_id -> transfer_info
        self.current_contact: Optional[str] = None
        
        # 创建界面
        self._create_widgets()
    
    def _create_widgets(self):
        """创建文件传输界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 标题
        title_frame = ttk.Frame(self.main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="文件传输", font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        
        # 发送文件按钮
        self.send_file_btn = ttk.Button(
            title_frame,
            text="发送文件",
            command=self._select_and_send_file,
            state=tk.DISABLED
        )
        self.send_file_btn.pack(side=tk.RIGHT)
        
        # 传输列表
        self._create_transfer_list()
        
        # 传输详情
        self._create_transfer_details()
    
    def _create_transfer_list(self):
        """创建传输列表"""
        list_frame = ttk.LabelFrame(self.main_frame, text="传输列表")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview
        columns = ("direction", "filename", "size", "progress", "status", "speed")
        self.transfer_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=8)
        
        # 设置列标题
        self.transfer_tree.heading("#0", text="时间")
        self.transfer_tree.heading("direction", text="方向")
        self.transfer_tree.heading("filename", text="文件名")
        self.transfer_tree.heading("size", text="大小")
        self.transfer_tree.heading("progress", text="进度")
        self.transfer_tree.heading("status", text="状态")
        self.transfer_tree.heading("speed", text="速度")
        
        # 设置列宽
        self.transfer_tree.column("#0", width=80, minwidth=80)
        self.transfer_tree.column("direction", width=60, minwidth=60)
        self.transfer_tree.column("filename", width=200, minwidth=150)
        self.transfer_tree.column("size", width=80, minwidth=80)
        self.transfer_tree.column("progress", width=100, minwidth=100)
        self.transfer_tree.column("status", width=80, minwidth=80)
        self.transfer_tree.column("speed", width=80, minwidth=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.transfer_tree.yview)
        self.transfer_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.transfer_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.transfer_tree.bind('<<TreeviewSelect>>', self._on_transfer_select)
        self.transfer_tree.bind('<Double-1>', self._on_transfer_double_click)
    
    def _create_transfer_details(self):
        """创建传输详情面板"""
        details_frame = ttk.LabelFrame(self.main_frame, text="传输详情")
        details_frame.pack(fill=tk.X)
        
        # 详情信息
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 文件信息
        ttk.Label(info_frame, text="文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.file_info_label = ttk.Label(info_frame, text="未选择传输")
        self.file_info_label.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(info_frame, text="对方:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.peer_info_label = ttk.Label(info_frame, text="-")
        self.peer_info_label.grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(info_frame, text="状态:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5))
        self.status_info_label = ttk.Label(info_frame, text="-")
        self.status_info_label.grid(row=2, column=1, sticky=tk.W)
        
        # 进度条
        progress_frame = ttk.Frame(details_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=200
        )
        self.progress_bar.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        
        self.progress_label = ttk.Label(progress_frame, text="0%")
        self.progress_label.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(details_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.pause_btn = ttk.Button(button_frame, text="暂停", command=self._pause_transfer, state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.cancel_btn = ttk.Button(button_frame, text="取消", command=self._cancel_transfer, state=tk.DISABLED)
        self.cancel_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.open_folder_btn = ttk.Button(button_frame, text="打开文件夹", command=self._open_file_folder, state=tk.DISABLED)
        self.open_folder_btn.pack(side=tk.RIGHT)
    
    def set_contact(self, contact_id: str):
        """设置当前联系人"""
        self.current_contact = contact_id
        self.send_file_btn.config(state=tk.NORMAL)
    
    def clear_contact(self):
        """清除当前联系人"""
        self.current_contact = None
        self.send_file_btn.config(state=tk.DISABLED)
    
    def _select_and_send_file(self):
        """选择并发送文件"""
        if not self.current_contact:
            messagebox.showwarning("发送文件", "请先选择联系人")
            return
        
        file_path = filedialog.askopenfilename(
            title="选择要发送的文件",
            filetypes=[
                ("所有文件", "*.*"),
                ("文档", "*.txt *.doc *.docx *.pdf"),
                ("图片", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("音频", "*.mp3 *.wav *.flac"),
                ("视频", "*.mp4 *.avi *.mkv *.mov"),
                ("压缩包", "*.zip *.rar *.7z")
            ]
        )
        
        if file_path:
            self._send_file(file_path)
    
    def _send_file(self, file_path: str):
        """发送文件"""
        try:
            # 获取文件信息
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            # 生成传输ID
            transfer_id = f"send_{int(time.time() * 1000)}"
            
            # 创建传输记录
            transfer_info = {
                'transfer_id': transfer_id,
                'direction': 'send',
                'filename': filename,
                'file_path': file_path,
                'file_size': file_size,
                'peer': self.current_contact,
                'status': 'preparing',
                'progress': 0,
                'speed': 0,
                'start_time': time.time(),
                'bytes_transferred': 0
            }
            
            self.transfers[transfer_id] = transfer_info
            self._add_transfer_to_list(transfer_info)
            
            # 调用发送回调
            if self.on_send_file:
                self.on_send_file(self.current_contact, file_path)
            
            # 更新状态
            self._update_transfer_status(transfer_id, 'sending')
            
        except Exception as e:
            messagebox.showerror("发送文件错误", f"发送文件失败: {e}")
    
    def add_incoming_transfer(self, transfer_id: str, sender: str, filename: str, file_size: int):
        """添加接收文件传输"""
        transfer_info = {
            'transfer_id': transfer_id,
            'direction': 'receive',
            'filename': filename,
            'file_path': '',
            'file_size': file_size,
            'peer': sender,
            'status': 'waiting',
            'progress': 0,
            'speed': 0,
            'start_time': time.time(),
            'bytes_transferred': 0
        }
        
        self.transfers[transfer_id] = transfer_info
        self._add_transfer_to_list(transfer_info)
        
        # 询问是否接受文件
        result = messagebox.askyesno(
            "接收文件",
            f"{sender} 要发送文件: {filename} ({self._format_file_size(file_size)})\n是否接受？"
        )
        
        if result:
            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存文件",
                initialvalue=filename,
                filetypes=[("所有文件", "*.*")]
            )
            
            if save_path:
                transfer_info['file_path'] = save_path
                self._update_transfer_status(transfer_id, 'receiving')
                return True
            else:
                self._update_transfer_status(transfer_id, 'cancelled')
                return False
        else:
            self._update_transfer_status(transfer_id, 'rejected')
            return False
    
    def update_transfer_progress(self, transfer_id: str, bytes_transferred: int):
        """更新传输进度"""
        if transfer_id not in self.transfers:
            return
        
        transfer_info = self.transfers[transfer_id]
        transfer_info['bytes_transferred'] = bytes_transferred
        
        # 计算进度
        progress = (bytes_transferred / transfer_info['file_size']) * 100
        transfer_info['progress'] = progress
        
        # 计算速度
        elapsed_time = time.time() - transfer_info['start_time']
        if elapsed_time > 0:
            speed = bytes_transferred / elapsed_time
            transfer_info['speed'] = speed
        
        # 更新界面
        self._update_transfer_in_list(transfer_info)
        
        # 如果选中了这个传输，更新详情
        selected = self.transfer_tree.selection()
        if selected and self.transfer_tree.item(selected[0])['values'][0] == transfer_id:
            self._update_transfer_details(transfer_info)
    
    def complete_transfer(self, transfer_id: str):
        """完成传输"""
        if transfer_id in self.transfers:
            self._update_transfer_status(transfer_id, 'completed')
    
    def fail_transfer(self, transfer_id: str, error_message: str):
        """传输失败"""
        if transfer_id in self.transfers:
            self.transfers[transfer_id]['error'] = error_message
            self._update_transfer_status(transfer_id, 'failed')
    
    def _add_transfer_to_list(self, transfer_info: Dict):
        """添加传输到列表"""
        timestamp = datetime.fromtimestamp(transfer_info['start_time']).strftime("%H:%M:%S")
        direction = "发送" if transfer_info['direction'] == 'send' else "接收"
        
        item_id = self.transfer_tree.insert("", tk.END, text=timestamp, values=(
            transfer_info['transfer_id'],
            direction,
            transfer_info['filename'],
            self._format_file_size(transfer_info['file_size']),
            f"{transfer_info['progress']:.1f}%",
            transfer_info['status'],
            self._format_speed(transfer_info['speed'])
        ))
        
        transfer_info['tree_item'] = item_id
    
    def _update_transfer_in_list(self, transfer_info: Dict):
        """更新列表中的传输信息"""
        if 'tree_item' not in transfer_info:
            return
        
        item_id = transfer_info['tree_item']
        direction = "发送" if transfer_info['direction'] == 'send' else "接收"
        
        self.transfer_tree.item(item_id, values=(
            transfer_info['transfer_id'],
            direction,
            transfer_info['filename'],
            self._format_file_size(transfer_info['file_size']),
            f"{transfer_info['progress']:.1f}%",
            transfer_info['status'],
            self._format_speed(transfer_info['speed'])
        ))
    
    def _update_transfer_status(self, transfer_id: str, status: str):
        """更新传输状态"""
        if transfer_id in self.transfers:
            self.transfers[transfer_id]['status'] = status
            self._update_transfer_in_list(self.transfers[transfer_id])
    
    def _update_transfer_details(self, transfer_info: Dict):
        """更新传输详情"""
        self.file_info_label.config(text=f"{transfer_info['filename']} ({self._format_file_size(transfer_info['file_size'])})")
        self.peer_info_label.config(text=transfer_info['peer'])
        self.status_info_label.config(text=transfer_info['status'])
        
        self.progress_var.set(transfer_info['progress'])
        self.progress_label.config(text=f"{transfer_info['progress']:.1f}%")
        
        # 更新按钮状态
        if transfer_info['status'] in ['sending', 'receiving']:
            self.pause_btn.config(state=tk.NORMAL)
            self.cancel_btn.config(state=tk.NORMAL)
        else:
            self.pause_btn.config(state=tk.DISABLED)
            self.cancel_btn.config(state=tk.DISABLED)
        
        if transfer_info['status'] == 'completed' and transfer_info['file_path']:
            self.open_folder_btn.config(state=tk.NORMAL)
        else:
            self.open_folder_btn.config(state=tk.DISABLED)
    
    def _on_transfer_select(self, event):
        """传输选择事件"""
        selected = self.transfer_tree.selection()
        if selected:
            values = self.transfer_tree.item(selected[0])['values']
            if values:
                transfer_id = values[0]
                if transfer_id in self.transfers:
                    self._update_transfer_details(self.transfers[transfer_id])
    
    def _on_transfer_double_click(self, event):
        """传输双击事件"""
        selected = self.transfer_tree.selection()
        if selected:
            values = self.transfer_tree.item(selected[0])['values']
            if values:
                transfer_id = values[0]
                if transfer_id in self.transfers:
                    transfer_info = self.transfers[transfer_id]
                    if transfer_info['status'] == 'completed' and transfer_info['file_path']:
                        self._open_file(transfer_info['file_path'])
    
    def _pause_transfer(self):
        """暂停传输"""
        messagebox.showinfo("暂停传输", "暂停功能待实现")
    
    def _cancel_transfer(self):
        """取消传输"""
        selected = self.transfer_tree.selection()
        if selected:
            values = self.transfer_tree.item(selected[0])['values']
            if values:
                transfer_id = values[0]
                result = messagebox.askyesno("取消传输", "确定要取消这个传输吗？")
                if result:
                    self._update_transfer_status(transfer_id, 'cancelled')
    
    def _open_file_folder(self):
        """打开文件所在文件夹"""
        selected = self.transfer_tree.selection()
        if selected:
            values = self.transfer_tree.item(selected[0])['values']
            if values:
                transfer_id = values[0]
                if transfer_id in self.transfers:
                    file_path = self.transfers[transfer_id]['file_path']
                    if file_path and os.path.exists(file_path):
                        folder_path = os.path.dirname(file_path)
                        os.startfile(folder_path)
    
    def _open_file(self, file_path: str):
        """打开文件"""
        if os.path.exists(file_path):
            os.startfile(file_path)
    
    def _format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def _format_speed(self, speed: float) -> str:
        """格式化传输速度"""
        if speed == 0:
            return "-"
        return f"{self._format_file_size(speed)}/s"
    
    def get_widget(self):
        """获取主组件"""
        return self.main_frame
    
    def pack(self, **kwargs):
        """打包组件"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局组件"""
        self.main_frame.grid(**kwargs)


# 示例使用
if __name__ == "__main__":
    root = tk.Tk()
    root.title("文件传输组件测试")
    root.geometry("700x500")
    
    def on_send_file(contact, file_path):
        print(f"发送文件到 {contact}: {file_path}")
    
    file_transfer = FileTransferWidget(root, on_send_file)
    file_transfer.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 设置测试联系人
    file_transfer.set_contact("TestUser")
    
    # 添加一些测试传输
    file_transfer.add_incoming_transfer("test1", "TestUser", "document.pdf", 1024*1024)
    file_transfer.update_transfer_progress("test1", 512*1024)
    
    root.mainloop()
