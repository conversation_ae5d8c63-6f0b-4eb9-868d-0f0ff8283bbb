"""
匿名加密通讯系统 - GUI模块
提供用户友好的图形界面
"""

import tkinter as tk
import tkinter.ttk as ttk

from .main_window import MainWindow
from .chat_widget import ChatWidget
from .file_transfer import FileTransferWidget

__all__ = [
    'MainWindow',
    'ChatWidget', 
    'FileTransferWidget'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Anonymous Crypto Chat Team"
__description__ = "Graphical user interface for anonymous encrypted communication"

# GUI配置
DEFAULT_GUI_CONFIG = {
    'window': {
        'title': '匿名加密通讯系统',
        'geometry': '1000x700',
        'min_size': (800, 600),
        'theme': 'default'
    },
    'chat': {
        'font_family': 'Arial',
        'font_size': 10,
        'max_messages': 1000,
        'auto_scroll': True,
        'show_timestamps': True,
        'message_colors': {
            'sent': '#0066cc',
            'received': '#009900',
            'system': '#666666',
            'error': '#cc0000'
        }
    },
    'file_transfer': {
        'max_file_size': 100 * 1024 * 1024,  # 100MB
        'chunk_size': 64 * 1024,  # 64KB
        'auto_accept': False,
        'download_folder': 'downloads'
    }
}


def get_default_gui_config():
    """获取默认GUI配置"""
    return DEFAULT_GUI_CONFIG.copy()


def create_main_application():
    """创建主应用程序"""
    return MainWindow()


def run_application():
    """运行应用程序"""
    app = create_main_application()
    app.run()


# 主题和样式
class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.themes = {
            'default': {
                'bg': '#f0f0f0',
                'fg': '#000000',
                'select_bg': '#0078d4',
                'select_fg': '#ffffff',
                'button_bg': '#e1e1e1',
                'button_fg': '#000000',
                'entry_bg': '#ffffff',
                'entry_fg': '#000000'
            },
            'dark': {
                'bg': '#2d2d2d',
                'fg': '#ffffff',
                'select_bg': '#0078d4',
                'select_fg': '#ffffff',
                'button_bg': '#404040',
                'button_fg': '#ffffff',
                'entry_bg': '#404040',
                'entry_fg': '#ffffff'
            },
            'blue': {
                'bg': '#e6f3ff',
                'fg': '#003366',
                'select_bg': '#0066cc',
                'select_fg': '#ffffff',
                'button_bg': '#cce6ff',
                'button_fg': '#003366',
                'entry_bg': '#ffffff',
                'entry_fg': '#003366'
            }
        }
        self.current_theme = 'default'
    
    def get_theme(self, theme_name: str = None):
        """获取主题"""
        if theme_name is None:
            theme_name = self.current_theme
        return self.themes.get(theme_name, self.themes['default'])
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            return True
        return False
    
    def apply_theme(self, widget, theme_name: str = None):
        """应用主题到组件"""
        theme = self.get_theme(theme_name)
        
        try:
            widget.configure(
                bg=theme['bg'],
                fg=theme['fg']
            )
        except:
            pass  # 某些组件可能不支持这些选项


# 全局主题管理器
theme_manager = ThemeManager()


# 工具函数
def center_window(window, width: int, height: int):
    """居中显示窗口"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")


def show_message_dialog(parent, title: str, message: str, dialog_type: str = "info"):
    """显示消息对话框"""
    import tkinter.messagebox as messagebox
    
    if dialog_type == "info":
        return messagebox.showinfo(title, message, parent=parent)
    elif dialog_type == "warning":
        return messagebox.showwarning(title, message, parent=parent)
    elif dialog_type == "error":
        return messagebox.showerror(title, message, parent=parent)
    elif dialog_type == "question":
        return messagebox.askyesno(title, message, parent=parent)
    else:
        return messagebox.showinfo(title, message, parent=parent)


def validate_input(text: str, input_type: str = "text") -> bool:
    """验证输入"""
    if input_type == "text":
        return len(text.strip()) > 0
    elif input_type == "user_id":
        return len(text.strip()) >= 3 and text.isalnum()
    elif input_type == "filename":
        import re
        return bool(re.match(r'^[^<>:"/\\|?*]+$', text))
    else:
        return True


def format_timestamp(timestamp, format_type: str = "time"):
    """格式化时间戳"""
    from datetime import datetime
    
    try:
        if isinstance(timestamp, str):
            dt = datetime.fromisoformat(timestamp)
        else:
            dt = datetime.fromtimestamp(timestamp)
        
        if format_type == "time":
            return dt.strftime("%H:%M:%S")
        elif format_type == "date":
            return dt.strftime("%Y-%m-%d")
        elif format_type == "datetime":
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return dt.strftime("%H:%M:%S")
    except:
        return str(timestamp)


def create_tooltip(widget, text: str):
    """创建工具提示"""
    def on_enter(event):
        tooltip = tk.Toplevel()
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
        
        label = tk.Label(
            tooltip,
            text=text,
            background="#ffffe0",
            relief="solid",
            borderwidth=1,
            font=("Arial", 8)
        )
        label.pack()
        
        widget.tooltip = tooltip
    
    def on_leave(event):
        if hasattr(widget, 'tooltip'):
            widget.tooltip.destroy()
            del widget.tooltip
    
    widget.bind("<Enter>", on_enter)
    widget.bind("<Leave>", on_leave)


# 自定义组件
class StatusBar:
    """状态栏组件"""
    
    def __init__(self, parent):
        self.frame = tk.Frame(parent, relief=tk.SUNKEN, bd=1)
        self.labels = {}
        self._create_default_labels()
    
    def _create_default_labels(self):
        """创建默认标签"""
        self.add_label("status", "就绪", side=tk.LEFT)
        self.add_label("connection", "未连接", side=tk.LEFT, color="red")
        self.add_label("time", "", side=tk.RIGHT)
        self._update_time()
    
    def add_label(self, name: str, text: str, side=tk.LEFT, color=None):
        """添加标签"""
        label = tk.Label(self.frame, text=text, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        if color:
            label.config(fg=color)
        label.pack(side=side, padx=2)
        self.labels[name] = label
    
    def update_label(self, name: str, text: str, color=None):
        """更新标签"""
        if name in self.labels:
            self.labels[name].config(text=text)
            if color:
                self.labels[name].config(fg=color)
    
    def _update_time(self):
        """更新时间"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.update_label("time", current_time)
        self.frame.after(1000, self._update_time)
    
    def pack(self, **kwargs):
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        self.frame.grid(**kwargs)


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent, title: str, message: str):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x120")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        center_window(self.dialog, 300, 120)
        
        # 消息标签
        tk.Label(self.dialog, text=message).pack(pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.dialog,
            variable=self.progress_var,
            maximum=100,
            length=250
        )
        self.progress_bar.pack(pady=10)
        
        # 取消按钮
        self.cancelled = False
        cancel_btn = tk.Button(self.dialog, text="取消", command=self._cancel)
        cancel_btn.pack(pady=5)
    
    def update_progress(self, value: float):
        """更新进度"""
        self.progress_var.set(value)
        self.dialog.update()
    
    def _cancel(self):
        """取消操作"""
        self.cancelled = True
        self.dialog.destroy()
    
    def is_cancelled(self):
        """检查是否被取消"""
        return self.cancelled
    
    def close(self):
        """关闭对话框"""
        self.dialog.destroy()


# 示例和测试
def test_gui_components():
    """测试GUI组件"""
    import tkinter as tk
    
    root = tk.Tk()
    root.title("GUI组件测试")
    root.geometry("600x400")
    
    # 测试聊天组件
    chat = ChatWidget(root)
    chat.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 设置测试数据
    chat.set_contact("TestUser", "测试用户", True)
    chat.add_message("TestUser", "你好！这是一条测试消息", is_sent=False)
    chat.add_message("TestUser", "我收到了你的消息", is_sent=True)
    
    # 测试状态栏
    status_bar = StatusBar(root)
    status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    status_bar.update_label("status", "测试中...")
    status_bar.update_label("connection", "已连接", "green")
    
    root.mainloop()


if __name__ == "__main__":
    # 运行GUI测试
    test_gui_components()
