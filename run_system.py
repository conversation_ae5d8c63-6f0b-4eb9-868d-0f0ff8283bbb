#!/usr/bin/env python3
"""
匿名加密通讯系统 - 完整启动脚本
整合SecureProtocol和KeyExchange协议消息系统
"""

import argparse
import asyncio
import sys
import os
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.network.client import AnonymousClient
from src.network.relay_server import RelayServer
from src.protocol.secure_protocol import SecureProtocol
from src.protocol.key_exchange import KeyExchange


def setup_logging(level=logging.INFO):
    """设置日志"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('anonymous_chat.log', encoding='utf-8')
        ]
    )


async def run_client(user_id: str, relay_servers: list):
    """运行客户端"""
    print(f"启动匿名客户端: {user_id}")
    print(f"连接中继服务器: {relay_servers}")
    
    try:
        # 创建客户端
        client = AnonymousClient(user_id, relay_servers)
        
        # 启动客户端
        await client.start()
        
    except KeyboardInterrupt:
        print("\n客户端已停止")
    except Exception as e:
        print(f"客户端运行错误: {e}")
        return 1
    return 0


async def run_relay_server(host='localhost', port=8001, server_id=None):
    """运行中继服务器"""
    print(f"启动中继服务器 {server_id or 'default'} on {host}:{port}")
    
    try:
        server = RelayServer(host, port, server_id)
        await server.start()
        
    except KeyboardInterrupt:
        print("\n中继服务器已停止")
    except Exception as e:
        print(f"中继服务器运行错误: {e}")
        return 1
    return 0


async def run_relay_network(ports=None):
    """运行中继网络"""
    if ports is None:
        ports = [8001, 8002, 8003]
    
    print(f"启动中继网络，端口: {ports}")
    
    try:
        # 启动多个中继服务器
        tasks = []
        for i, port in enumerate(ports):
            server_id = f"relay_{port}"
            task = asyncio.create_task(
                run_relay_server('localhost', port, server_id)
            )
            tasks.append(task)
        
        # 等待所有服务器
        await asyncio.gather(*tasks)
        
    except KeyboardInterrupt:
        print("\n中继网络已停止")
    except Exception as e:
        print(f"中继网络运行错误: {e}")
        return 1
    return 0


async def test_protocol_system():
    """测试协议消息系统"""
    print("=== 测试协议消息系统 ===")
    
    try:
        # 导入测试模块
        from test_protocol_system import test_protocol_system, test_error_handling
        
        # 运行测试
        await test_protocol_system()
        await test_error_handling()
        
        print("\n🎉 协议消息系统测试成功！")
        return 0
        
    except Exception as e:
        print(f"协议测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def test_full_system():
    """测试完整系统"""
    print("=== 测试完整系统 ===")
    
    try:
        # 启动中继服务器
        relay_task = asyncio.create_task(
            run_relay_server('localhost', 8001, 'test_relay')
        )
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
        # 启动两个客户端进行通信测试
        client1_task = asyncio.create_task(
            test_client_communication("Alice", ["ws://localhost:8001"])
        )
        
        client2_task = asyncio.create_task(
            test_client_communication("Bob", ["ws://localhost:8001"])
        )
        
        # 等待测试完成
        await asyncio.sleep(10)
        
        # 取消任务
        relay_task.cancel()
        client1_task.cancel()
        client2_task.cancel()
        
        print("✓ 完整系统测试完成")
        return 0
        
    except Exception as e:
        print(f"完整系统测试失败: {e}")
        return 1


async def test_client_communication(user_id: str, relay_servers: list):
    """测试客户端通信"""
    print(f"启动测试客户端: {user_id}")
    
    try:
        client = AnonymousClient(user_id, relay_servers)
        
        # 启动客户端
        client_task = asyncio.create_task(client.start())
        
        # 等待连接建立
        await asyncio.sleep(3)
        
        if user_id == "Alice":
            # Alice 添加 Bob 为联系人
            success = await client.add_contact("Bob")
            if success:
                print(f"✓ {user_id} 成功添加 Bob 为联系人")
                
                # 发送测试消息
                await asyncio.sleep(2)
                success = await client.send_message("Bob", f"Hello from {user_id}!")
                if success:
                    print(f"✓ {user_id} 成功发送消息")
                else:
                    print(f"✗ {user_id} 发送消息失败")
        
        # 保持运行一段时间
        await asyncio.sleep(5)
        
        # 停止客户端
        client_task.cancel()
        
    except Exception as e:
        print(f"客户端 {user_id} 测试失败: {e}")


def show_system_info():
    """显示系统信息"""
    print("=== 匿名加密通讯系统信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"项目路径: {project_root}")
    
    # 检查依赖
    try:
        import cryptography
        print(f"cryptography版本: {cryptography.__version__}")
    except ImportError:
        print("cryptography: 未安装")
    
    try:
        import websockets
        print(f"websockets版本: {websockets.__version__}")
    except ImportError:
        print("websockets: 未安装")
    
    try:
        import tkinter
        print(f"tkinter: 可用")
    except ImportError:
        print("tkinter: 不可用")
    
    print("\n协议消息系统组件:")
    print("  ✓ SecureProtocol - 安全协议实现")
    print("  ✓ KeyExchange - 密钥交换实现")
    print("  ✓ Message - 消息协议定义")
    print("  ✓ AnonymousClient - 匿名客户端")
    print("  ✓ RelayServer - 中继服务器")
    
    print("\n可用命令:")
    print("  python run_system.py client [user_id] [relay_servers]  - 启动客户端")
    print("  python run_system.py relay [port]                     - 启动中继服务器")
    print("  python run_system.py network [ports]                  - 启动中继网络")
    print("  python run_system.py test-protocol                    - 测试协议消息系统")
    print("  python run_system.py test-full                        - 测试完整系统")
    print("  python run_system.py info                             - 显示系统信息")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="匿名加密通讯系统 - 完整版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python run_system.py client Alice ws://localhost:8001,ws://localhost:8002
  python run_system.py relay 8001
  python run_system.py network 8001,8002,8003
  python run_system.py test-protocol
  python run_system.py test-full
  python run_system.py info
        """
    )
    
    parser.add_argument('mode', choices=[
        'client', 'relay', 'network', 'test-protocol', 'test-full', 'info'
    ], help='运行模式')
    
    parser.add_argument('args', nargs='*', help='额外参数')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    try:
        if args.mode == 'client':
            # 客户端模式
            user_id = args.args[0] if args.args else "Anonymous"
            relay_servers = args.args[1].split(',') if len(args.args) > 1 else ["ws://localhost:8001"]
            
            return asyncio.run(run_client(user_id, relay_servers))
            
        elif args.mode == 'relay':
            # 中继服务器模式
            port = int(args.args[0]) if args.args else 8001
            
            return asyncio.run(run_relay_server('localhost', port))
            
        elif args.mode == 'network':
            # 中继网络模式
            ports_str = args.args[0] if args.args else "8001,8002,8003"
            ports = [int(p) for p in ports_str.split(',')]
            
            return asyncio.run(run_relay_network(ports))
            
        elif args.mode == 'test-protocol':
            # 测试协议消息系统
            return asyncio.run(test_protocol_system())
            
        elif args.mode == 'test-full':
            # 测试完整系统
            return asyncio.run(test_full_system())
            
        elif args.mode == 'info':
            # 显示系统信息
            show_system_info()
            return 0
            
    except KeyboardInterrupt:
        print("\n程序已停止")
        return 0
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main()) 