"""
密钥交换模块 - 实现ECDH密钥交换协议
支持安全的密钥协商和会话密钥生成
"""

import secrets
import hashlib
from typing import Tuple, Optional
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.primitives.serialization import Encoding, PublicFormat
import base64
import time


class ECDHKeyExchange:
    """ECDH密钥交换类"""
    
    def __init__(self, curve=ec.SECP384R1()):
        self.curve = curve
        self.key_size = 32  # 派生密钥长度 (AES-256)
    
    def generate_keypair(self) -> Tuple[ec.EllipticCurvePrivateKey, ec.EllipticCurvePublicKey]:
        """生成ECDH密钥对"""
        private_key = ec.generate_private_key(self.curve)
        public_key = private_key.public_key()
        return private_key, public_key
    
    def serialize_public_key(self, public_key: ec.EllipticCurvePublicKey) -> bytes:
        """序列化公钥用于传输"""
        return public_key.public_bytes(
            encoding=Encoding.X962,
            format=PublicFormat.UncompressedPoint
        )
    
    def deserialize_public_key(self, key_bytes: bytes) -> ec.EllipticCurvePublicKey:
        """反序列化公钥"""
        return ec.EllipticCurvePublicKey.from_encoded_point(self.curve, key_bytes)
    
    def perform_exchange(self, private_key: ec.EllipticCurvePrivateKey, 
                        peer_public_key: ec.EllipticCurvePublicKey,
                        info: bytes = b"") -> bytes:
        """
        执行ECDH密钥交换
        返回派生的共享密钥
        """
        # 计算共享密钥
        shared_key = private_key.exchange(ec.ECDH(), peer_public_key)
        
        # 使用HKDF派生最终密钥
        hkdf = HKDF(
            algorithm=hashes.SHA256(),
            length=self.key_size,
            salt=None,
            info=info,
        )
        derived_key = hkdf.derive(shared_key)
        return derived_key
    
    def create_key_exchange_message(self, public_key: ec.EllipticCurvePublicKey, 
                                   sender_id: str, timestamp: Optional[int] = None) -> dict:
        """创建密钥交换消息"""
        if timestamp is None:
            timestamp = int(time.time())
        
        public_key_bytes = self.serialize_public_key(public_key)
        
        return {
            'sender_id': sender_id,
            'public_key': base64.b64encode(public_key_bytes).decode('ascii'),
            'timestamp': timestamp,
            'curve': 'secp384r1'  # 曲线标识
        }
    
    def parse_key_exchange_message(self, message: dict) -> Tuple[str, ec.EllipticCurvePublicKey, int]:
        """解析密钥交换消息"""
        sender_id = message['sender_id']
        timestamp = message['timestamp']
        
        public_key_bytes = base64.b64decode(message['public_key'])
        public_key = self.deserialize_public_key(public_key_bytes)
        
        return sender_id, public_key, timestamp


class SessionKeyManager:
    """会话密钥管理器"""
    
    def __init__(self):
        self.ecdh = ECDHKeyExchange()
        self.sessions = {}  # 存储会话密钥
    
    def initiate_key_exchange(self, session_id: str, sender_id: str) -> Tuple[dict, ec.EllipticCurvePrivateKey]:
        """发起密钥交换"""
        # 生成临时密钥对
        private_key, public_key = self.ecdh.generate_keypair()
        
        # 创建密钥交换消息
        message = self.ecdh.create_key_exchange_message(public_key, sender_id)
        
        # 存储私钥用于后续交换
        self.sessions[session_id] = {
            'private_key': private_key,
            'sender_id': sender_id,
            'status': 'initiated',
            'timestamp': message['timestamp']
        }
        
        return message, private_key
    
    def complete_key_exchange(self, session_id: str, peer_message: dict) -> bytes:
        """完成密钥交换"""
        if session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        session = self.sessions[session_id]
        if session['status'] != 'initiated':
            raise ValueError("会话状态错误")
        
        # 解析对方的公钥
        peer_id, peer_public_key, peer_timestamp = self.ecdh.parse_key_exchange_message(peer_message)
        
        # 创建上下文信息
        info = f"{session['sender_id']}:{peer_id}:{session['timestamp']}:{peer_timestamp}".encode()
        
        # 执行密钥交换
        shared_key = self.ecdh.perform_exchange(
            session['private_key'], 
            peer_public_key, 
            info
        )
        
        # 更新会话状态
        session.update({
            'peer_id': peer_id,
            'shared_key': shared_key,
            'status': 'completed',
            'peer_timestamp': peer_timestamp
        })
        
        return shared_key
    
    def respond_to_key_exchange(self, peer_message: dict, responder_id: str) -> Tuple[dict, bytes]:
        """响应密钥交换请求"""
        # 解析发起方的公钥
        peer_id, peer_public_key, peer_timestamp = self.ecdh.parse_key_exchange_message(peer_message)
        
        # 生成响应方的密钥对
        private_key, public_key = self.ecdh.generate_keypair()
        
        # 创建响应消息
        response_message = self.ecdh.create_key_exchange_message(public_key, responder_id)
        
        # 创建上下文信息
        info = f"{peer_id}:{responder_id}:{peer_timestamp}:{response_message['timestamp']}".encode()
        
        # 执行密钥交换
        shared_key = self.ecdh.perform_exchange(private_key, peer_public_key, info)
        
        # 创建会话ID
        session_id = hashlib.sha256(f"{peer_id}:{responder_id}:{peer_timestamp}".encode()).hexdigest()[:16]
        
        # 存储会话信息
        self.sessions[session_id] = {
            'private_key': private_key,
            'sender_id': responder_id,
            'peer_id': peer_id,
            'shared_key': shared_key,
            'status': 'completed',
            'timestamp': response_message['timestamp'],
            'peer_timestamp': peer_timestamp
        }
        
        return response_message, shared_key
    
    def get_session_key(self, session_id: str) -> Optional[bytes]:
        """获取会话密钥"""
        session = self.sessions.get(session_id)
        if session and session['status'] == 'completed':
            return session['shared_key']
        return None
    
    def remove_session(self, session_id: str) -> None:
        """删除会话"""
        if session_id in self.sessions:
            # 安全清除密钥
            session = self.sessions[session_id]
            if 'shared_key' in session:
                # 用随机数据覆盖密钥
                session['shared_key'] = secrets.token_bytes(len(session['shared_key']))
            del self.sessions[session_id]
    
    def cleanup_expired_sessions(self, max_age: int = 3600) -> None:
        """清理过期会话"""
        current_time = int(time.time())
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            if current_time - session['timestamp'] > max_age:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.remove_session(session_id)


class PerfectForwardSecrecy:
    """完美前向保密实现"""
    
    def __init__(self):
        self.key_manager = SessionKeyManager()
        self.ratchet_keys = {}  # 棘轮密钥链
    
    def initialize_ratchet(self, session_id: str, initial_key: bytes) -> None:
        """初始化密钥棘轮"""
        self.ratchet_keys[session_id] = {
            'current_key': initial_key,
            'key_number': 0,
            'used_keys': set()
        }
    
    def advance_ratchet(self, session_id: str) -> bytes:
        """推进密钥棘轮，生成新的密钥"""
        if session_id not in self.ratchet_keys:
            raise ValueError("棘轮未初始化")
        
        ratchet = self.ratchet_keys[session_id]
        
        # 使用当前密钥派生下一个密钥
        hkdf = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=None,
            info=f"ratchet_{ratchet['key_number'] + 1}".encode(),
        )
        
        next_key = hkdf.derive(ratchet['current_key'])
        
        # 标记当前密钥为已使用
        ratchet['used_keys'].add(ratchet['key_number'])
        
        # 更新棘轮状态
        ratchet['current_key'] = next_key
        ratchet['key_number'] += 1
        
        return next_key
    
    def get_current_key(self, session_id: str) -> Optional[bytes]:
        """获取当前密钥"""
        ratchet = self.ratchet_keys.get(session_id)
        if ratchet:
            return ratchet['current_key']
        return None


# 示例使用
if __name__ == "__main__":
    print("=== ECDH密钥交换示例 ===")
    
    # 创建两个会话密钥管理器（模拟Alice和Bob）
    alice_manager = SessionKeyManager()
    bob_manager = SessionKeyManager()
    
    # Alice发起密钥交换
    session_id = "test_session_001"
    alice_message, alice_private = alice_manager.initiate_key_exchange(session_id, "Alice")
    print(f"Alice发起密钥交换: {alice_message['sender_id']}")
    
    # Bob响应密钥交换
    bob_response, bob_shared_key = bob_manager.respond_to_key_exchange(alice_message, "Bob")
    print(f"Bob响应密钥交换: {bob_response['sender_id']}")
    
    # Alice完成密钥交换
    alice_shared_key = alice_manager.complete_key_exchange(session_id, bob_response)
    
    # 验证双方得到相同的共享密钥
    print(f"密钥交换成功: {alice_shared_key == bob_shared_key}")
    print(f"共享密钥: {base64.b64encode(alice_shared_key).decode()}")
    
    # 完美前向保密示例
    print("\n=== 完美前向保密示例 ===")
    pfs = PerfectForwardSecrecy()
    pfs.initialize_ratchet(session_id, alice_shared_key)
    
    # 生成几个新密钥
    for i in range(3):
        new_key = pfs.advance_ratchet(session_id)
        print(f"密钥 {i+1}: {base64.b64encode(new_key).decode()[:32]}...")
