import tkinter as tk
from tkinter import ttk, messagebox
import secrets
import string
import re

class UserIdDialog:
    """用户身份设置对话框"""
    
    def __init__(self, parent):
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("设置用户身份")
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="设置您的匿名身份", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 显示名称
        name_frame = ttk.Frame(main_frame)
        name_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(name_frame, text="显示名称:").pack(side=tk.LEFT)
        self.name_entry = ttk.Entry(name_frame)
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 用户ID
        id_frame = ttk.Frame(main_frame)
        id_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(id_frame, text="用户ID:").pack(side=tk.LEFT)
        self.id_entry = ttk.Entry(id_frame)
        self.id_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 生成随机ID按钮
        self.generate_btn = ttk.Button(id_frame, text="随机生成", command=self._generate_id)
        self.generate_btn.pack(side=tk.LEFT)
        
        # 提示信息
        hint_text = """
提示:
1. 显示名称可以使用任何字符，建议使用易记的名称
2. 用户ID用于唯一标识，建议使用随机生成的ID
3. 请妥善保管您的用户ID，其他用户需要通过ID添加您为联系人
        """
        hint_label = ttk.Label(main_frame, text=hint_text, justify=tk.LEFT, wraplength=350)
        hint_label.pack(pady=20)
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 确定按钮
        self.ok_btn = ttk.Button(btn_frame, text="确定", command=self._on_ok)
        self.ok_btn.pack(side=tk.RIGHT, padx=5)
        
        # 取消按钮
        self.cancel_btn = ttk.Button(btn_frame, text="取消", command=self._on_cancel)
        self.cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # 绑定回车键
        self.dialog.bind("<Return>", lambda e: self._on_ok())
        self.dialog.bind("<Escape>", lambda e: self._on_cancel())
        
        # 设置焦点
        self.name_entry.focus()
    
    def _generate_id(self):
        """生成随机用户ID"""
        # 生成16位随机字符串
        alphabet = string.ascii_letters + string.digits
        random_id = ''.join(secrets.choice(alphabet) for _ in range(16))
        self.id_entry.delete(0, tk.END)
        self.id_entry.insert(0, random_id)
    
    def _validate(self):
        """验证输入"""
        name = self.name_entry.get().strip()
        user_id = self.id_entry.get().strip()
        
        if not name:
            messagebox.showwarning("验证", "请输入显示名称")
            self.name_entry.focus()
            return False
        
        if not user_id:
            messagebox.showwarning("验证", "请输入用户ID")
            self.id_entry.focus()
            return False
        
        # 验证用户ID格式
        if not re.match(r'^[a-zA-Z0-9_-]{4,32}$', user_id):
            messagebox.showwarning(
                "验证",
                "用户ID只能包含字母、数字、下划线和连字符，长度在4-32位之间"
            )
            self.id_entry.focus()
            return False
        
        return True
    
    def _on_ok(self):
        """确定按钮事件"""
        if not self._validate():
            return
        
        self.result = (
            self.name_entry.get().strip(),
            self.id_entry.get().strip()
        )
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()


class AddContactDialog:
    """添加联系人对话框"""
    
    def __init__(self, parent):
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加联系人")
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="添加新联系人", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 联系人ID
        id_frame = ttk.Frame(main_frame)
        id_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(id_frame, text="联系人ID:").pack(side=tk.LEFT)
        self.id_entry = ttk.Entry(id_frame)
        self.id_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 显示名称
        name_frame = ttk.Frame(main_frame)
        name_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(name_frame, text="显示名称:").pack(side=tk.LEFT)
        self.name_entry = ttk.Entry(name_frame)
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 确定按钮
        self.ok_btn = ttk.Button(btn_frame, text="确定", command=self._on_ok)
        self.ok_btn.pack(side=tk.RIGHT, padx=5)
        
        # 取消按钮
        self.cancel_btn = ttk.Button(btn_frame, text="取消", command=self._on_cancel)
        self.cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # 绑定回车键
        self.dialog.bind("<Return>", lambda e: self._on_ok())
        self.dialog.bind("<Escape>", lambda e: self._on_cancel())
        
        # 设置焦点
        self.id_entry.focus()
    
    def _validate(self):
        """验证输入"""
        contact_id = self.id_entry.get().strip()
        name = self.name_entry.get().strip()
        
        if not contact_id:
            messagebox.showwarning("验证", "请输入联系人ID")
            self.id_entry.focus()
            return False
        
        if not name:
            messagebox.showwarning("验证", "请输入显示名称")
            self.name_entry.focus()
            return False
        
        # 验证联系人ID格式
        if not re.match(r'^[a-zA-Z0-9_-]{4,32}$', contact_id):
            messagebox.showwarning(
                "验证",
                "联系人ID只能包含字母、数字、下划线和连字符，长度在4-32位之间"
            )
            self.id_entry.focus()
            return False
        
        return True
    
    def _on_ok(self):
        """确定按钮事件"""
        if not self._validate():
            return
        
        self.result = (
            self.id_entry.get().strip(),
            self.name_entry.get().strip()
        )
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()


class KeyManagementDialog:
    """密钥管理对话框"""
    
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("密钥管理")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="密钥管理", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 密钥信息
        info_frame = ttk.LabelFrame(main_frame, text="当前密钥信息")
        info_frame.pack(fill=tk.X, pady=10)
        
        # RSA密钥信息
        rsa_frame = ttk.Frame(info_frame)
        rsa_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(rsa_frame, text="RSA密钥:").pack(side=tk.LEFT)
        self.rsa_label = ttk.Label(rsa_frame, text="4096位")
        self.rsa_label.pack(side=tk.LEFT, padx=5)
        
        # ECDH密钥信息
        ecdh_frame = ttk.Frame(info_frame)
        ecdh_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(ecdh_frame, text="ECDH密钥:").pack(side=tk.LEFT)
        self.ecdh_label = ttk.Label(ecdh_frame, text="SECP384R1")
        self.ecdh_label.pack(side=tk.LEFT, padx=5)
        
        # AES密钥信息
        aes_frame = ttk.Frame(info_frame)
        aes_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(aes_frame, text="AES密钥:").pack(side=tk.LEFT)
        self.aes_label = ttk.Label(aes_frame, text="256位 GCM模式")
        self.aes_label.pack(side=tk.LEFT, padx=5)
        
        # 操作按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=20)
        
        # 生成新密钥按钮
        self.generate_btn = ttk.Button(btn_frame, text="生成新密钥", command=self._generate_keys)
        self.generate_btn.pack(side=tk.LEFT, padx=5)
        
        # 导入密钥按钮
        self.import_btn = ttk.Button(btn_frame, text="导入密钥", command=self._import_keys)
        self.import_btn.pack(side=tk.LEFT, padx=5)
        
        # 导出密钥按钮
        self.export_btn = ttk.Button(btn_frame, text="导出密钥", command=self._export_keys)
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        # 关闭按钮
        self.close_btn = ttk.Button(main_frame, text="关闭", command=self._on_close)
        self.close_btn.pack(side=tk.RIGHT, pady=20)
        
        # 绑定Escape键
        self.dialog.bind("<Escape>", lambda e: self._on_close())
    
    def _generate_keys(self):
        """生成新密钥"""
        if messagebox.askyesno("生成密钥", "确定要生成新的密钥对吗？这将使当前的密钥失效。"):
            # TODO: 实现密钥生成逻辑
            messagebox.showinfo("生成密钥", "新密钥生成成功")
    
    def _import_keys(self):
        """导入密钥"""
        # TODO: 实现密钥导入逻辑
        messagebox.showinfo("导入密钥", "密钥导入成功")
    
    def _export_keys(self):
        """导出密钥"""
        # TODO: 实现密钥导出逻辑
        messagebox.showinfo("导出密钥", "密钥导出成功")
    
    def _on_close(self):
        """关闭对话框"""
        self.dialog.destroy()


class SecuritySettingsDialog:
    """安全设置对话框"""
    
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("安全设置")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="安全设置", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 加密设置
        encryption_frame = ttk.LabelFrame(main_frame, text="加密设置")
        encryption_frame.pack(fill=tk.X, pady=10)
        
        # AES密钥长度
        aes_frame = ttk.Frame(encryption_frame)
        aes_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(aes_frame, text="AES密钥长度:").pack(side=tk.LEFT)
        self.aes_var = tk.StringVar(value="256")
        ttk.Radiobutton(aes_frame, text="256位", variable=self.aes_var, value="256").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(aes_frame, text="192位", variable=self.aes_var, value="192").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(aes_frame, text="128位", variable=self.aes_var, value="128").pack(side=tk.LEFT, padx=5)
        
        # RSA密钥长度
        rsa_frame = ttk.Frame(encryption_frame)
        rsa_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(rsa_frame, text="RSA密钥长度:").pack(side=tk.LEFT)
        self.rsa_var = tk.StringVar(value="4096")
        ttk.Radiobutton(rsa_frame, text="4096位", variable=self.rsa_var, value="4096").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(rsa_frame, text="3072位", variable=self.rsa_var, value="3072").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(rsa_frame, text="2048位", variable=self.rsa_var, value="2048").pack(side=tk.LEFT, padx=5)
        
        # 匿名设置
        anonymous_frame = ttk.LabelFrame(main_frame, text="匿名设置")
        anonymous_frame.pack(fill=tk.X, pady=10)
        
        # 混淆流量
        traffic_frame = ttk.Frame(anonymous_frame)
        traffic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.traffic_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(traffic_frame, text="启用流量混淆", variable=self.traffic_var).pack(side=tk.LEFT)
        
        # 虚假流量
        dummy_frame = ttk.Frame(anonymous_frame)
        dummy_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.dummy_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(dummy_frame, text="生成虚假流量", variable=self.dummy_var).pack(side=tk.LEFT)
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=20)
        
        # 确定按钮
        self.ok_btn = ttk.Button(btn_frame, text="确定", command=self._on_ok)
        self.ok_btn.pack(side=tk.RIGHT, padx=5)
        
        # 取消按钮
        self.cancel_btn = ttk.Button(btn_frame, text="取消", command=self._on_cancel)
        self.cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # 绑定回车键和Escape键
        self.dialog.bind("<Return>", lambda e: self._on_ok())
        self.dialog.bind("<Escape>", lambda e: self._on_cancel())
    
    def _on_ok(self):
        """确定按钮事件"""
        # TODO: 保存设置
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()


class HelpDialog:
    """帮助对话框"""
    
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("使用说明")
        self.dialog.geometry("600x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="使用说明", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 帮助内容
        help_text = """
匿名加密通讯系统使用说明

1. 基本功能
   - 设置用户身份：首次使用时需要设置显示名称和用户ID
   - 添加联系人：通过联系人的用户ID添加联系人
   - 发送消息：选择联系人后即可发送文本消息和文件
   - 查看历史：可以查看与每个联系人的聊天历史记录

2. 安全功能
   - 端到端加密：所有消息都经过端到端加密
   - 匿名通信：使用洋葱路由实现匿名通信
   - 流量混淆：自动混淆通信流量
   - 密钥管理：可以管理和更新加密密钥

3. 快捷键
   - Ctrl+Return：发送消息
   - Ctrl+S：发送消息
   - Ctrl+N：添加联系人
   - F5：连接/断开连接
   - Ctrl+Q：退出程序

4. 注意事项
   - 请妥善保管您的用户ID和密钥
   - 定期更新密钥以提高安全性
   - 不要将密钥分享给他人
   - 使用完毕后请正常退出程序
        """
        
        # 创建文本框
        self.text = scrolledtext.ScrolledText(
            main_frame,
            wrap=tk.WORD,
            width=70,
            height=15,
            font=("Arial", 10)
        )
        self.text.pack(fill=tk.BOTH, expand=True)
        self.text.insert("1.0", help_text.strip())
        self.text.config(state=tk.DISABLED)
        
        # 关闭按钮
        self.close_btn = ttk.Button(main_frame, text="关闭", command=self._on_close)
        self.close_btn.pack(side=tk.RIGHT, pady=20)
        
        # 绑定Escape键
        self.dialog.bind("<Escape>", lambda e: self._on_close())
    
    def _on_close(self):
        """关闭对话框"""
        self.dialog.destroy()


class AboutDialog:
    """关于对话框"""
    
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("关于")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        # 创建界面
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="匿名加密通讯系统", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 版本信息
        version_label = ttk.Label(main_frame, text="版本 1.0.0", font=("Arial", 10))
        version_label.pack()
        
        # 作者信息
        author_label = ttk.Label(main_frame, text="作者: Anonymous", font=("Arial", 10))
        author_label.pack(pady=5)
        
        # 描述信息
        description = """
这是一个基于Python开发的匿名加密通讯系统。
系统使用RSA和AES加密算法保证通信安全，
使用洋葱路由实现匿名通信。

主要特性:
- 端到端加密
- 匿名通信
- 文件传输
- 流量混淆
        """
        desc_label = ttk.Label(main_frame, text=description.strip(), justify=tk.CENTER, wraplength=350)
        desc_label.pack(pady=20)
        
        # 版权信息
        copyright_label = ttk.Label(main_frame, text="Copyright © 2024", font=("Arial", 10))
        copyright_label.pack(pady=20)
        
        # 关闭按钮
        self.close_btn = ttk.Button(main_frame, text="关闭", command=self._on_close)
        self.close_btn.pack(side=tk.BOTTOM)
        
        # 绑定Escape键
        self.dialog.bind("<Escape>", lambda e: self._on_close())
    
    def _on_close(self):
        """关闭对话框"""
        self.dialog.destroy() 