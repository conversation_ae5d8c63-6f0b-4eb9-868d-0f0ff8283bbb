"""
密钥管理系统
提供安全的密钥生成、存储、分发和轮换机制
"""

import os
import json
import secrets
import sqlite3
from typing import Dict, Optional, List, Tuple
from pathlib import Path
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa, ec
from cryptography.fernet import Fernet
import base64
import time
import threading
from .symmetric import SymmetricCrypto
from .asymmetric import RSACrypto, ECCCrypto
from .hashing import SecureHash, KeyDerivation


class SecureKeyStorage:
    """安全密钥存储类"""
    
    def __init__(self, storage_path: str = "keystore.db", master_password: Optional[str] = None):
        self.storage_path = storage_path
        self.master_key = None
        self.fernet = None
        self.lock = threading.RLock()
        
        # 初始化存储
        self._init_storage()
        
        if master_password:
            self.unlock(master_password)
    
    def _init_storage(self):
        """初始化密钥存储数据库"""
        with sqlite3.connect(self.storage_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS keys (
                    key_id TEXT PRIMARY KEY,
                    key_type TEXT NOT NULL,
                    encrypted_key BLOB NOT NULL,
                    metadata TEXT,
                    created_at INTEGER NOT NULL,
                    expires_at INTEGER,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS master_key_info (
                    id INTEGER PRIMARY KEY,
                    salt BLOB NOT NULL,
                    key_hash BLOB NOT NULL,
                    created_at INTEGER NOT NULL
                )
            ''')
            conn.commit()
    
    def set_master_password(self, password: str) -> bool:
        """设置主密码"""
        try:
            # 派生主密钥
            key, salt = KeyDerivation.pbkdf2(password, iterations=200000)
            key_hash = SecureHash.sha256(key)
            
            with sqlite3.connect(self.storage_path) as conn:
                # 检查是否已有主密码
                cursor = conn.execute('SELECT COUNT(*) FROM master_key_info')
                if cursor.fetchone()[0] > 0:
                    return False  # 主密码已存在
                
                # 存储盐值和密钥哈希
                conn.execute(
                    'INSERT INTO master_key_info (salt, key_hash, created_at) VALUES (?, ?, ?)',
                    (salt, key_hash, int(time.time()))
                )
                conn.commit()
            
            self.master_key = key
            self.fernet = Fernet(base64.urlsafe_b64encode(key))
            return True
        except Exception:
            return False
    
    def unlock(self, password: str) -> bool:
        """解锁密钥存储"""
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.execute('SELECT salt, key_hash FROM master_key_info ORDER BY id DESC LIMIT 1')
                row = cursor.fetchone()
                if not row:
                    return False
                
                salt, stored_hash = row
                
                # 派生密钥并验证
                key, _ = KeyDerivation.pbkdf2(password, salt, iterations=200000)
                key_hash = SecureHash.sha256(key)
                
                if key_hash != stored_hash:
                    return False
                
                self.master_key = key
                self.fernet = Fernet(base64.urlsafe_b64encode(key))
                return True
        except Exception:
            return False
    
    def is_unlocked(self) -> bool:
        """检查是否已解锁"""
        return self.master_key is not None and self.fernet is not None
    
    def store_key(self, key_id: str, key_data: bytes, key_type: str, 
                  metadata: Optional[Dict] = None, expires_at: Optional[int] = None) -> bool:
        """存储密钥"""
        if not self.is_unlocked():
            raise RuntimeError("密钥存储未解锁")
        
        try:
            with self.lock:
                # 加密密钥数据
                encrypted_key = self.fernet.encrypt(key_data)
                metadata_json = json.dumps(metadata or {})
                
                with sqlite3.connect(self.storage_path) as conn:
                    conn.execute('''
                        INSERT OR REPLACE INTO keys 
                        (key_id, key_type, encrypted_key, metadata, created_at, expires_at, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, 1)
                    ''', (key_id, key_type, encrypted_key, metadata_json, int(time.time()), expires_at))
                    conn.commit()
                return True
        except Exception:
            return False
    
    def retrieve_key(self, key_id: str) -> Optional[Tuple[bytes, str, Dict]]:
        """检索密钥"""
        if not self.is_unlocked():
            raise RuntimeError("密钥存储未解锁")
        
        try:
            with self.lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.execute('''
                        SELECT encrypted_key, key_type, metadata, expires_at 
                        FROM keys WHERE key_id = ? AND is_active = 1
                    ''', (key_id,))
                    row = cursor.fetchone()
                    
                    if not row:
                        return None
                    
                    encrypted_key, key_type, metadata_json, expires_at = row
                    
                    # 检查过期时间
                    if expires_at and int(time.time()) > expires_at:
                        return None
                    
                    # 解密密钥
                    key_data = self.fernet.decrypt(encrypted_key)
                    metadata = json.loads(metadata_json)
                    
                    return key_data, key_type, metadata
        except Exception:
            return None
    
    def delete_key(self, key_id: str) -> bool:
        """删除密钥"""
        try:
            with self.lock:
                with sqlite3.connect(self.storage_path) as conn:
                    conn.execute('UPDATE keys SET is_active = 0 WHERE key_id = ?', (key_id,))
                    conn.commit()
                return True
        except Exception:
            return False
    
    def list_keys(self, key_type: Optional[str] = None) -> List[Dict]:
        """列出密钥"""
        if not self.is_unlocked():
            raise RuntimeError("密钥存储未解锁")
        
        try:
            with self.lock:
                with sqlite3.connect(self.storage_path) as conn:
                    if key_type:
                        cursor = conn.execute('''
                            SELECT key_id, key_type, metadata, created_at, expires_at
                            FROM keys WHERE key_type = ? AND is_active = 1
                        ''', (key_type,))
                    else:
                        cursor = conn.execute('''
                            SELECT key_id, key_type, metadata, created_at, expires_at
                            FROM keys WHERE is_active = 1
                        ''')
                    
                    keys = []
                    for row in cursor.fetchall():
                        key_id, key_type, metadata_json, created_at, expires_at = row
                        metadata = json.loads(metadata_json)
                        keys.append({
                            'key_id': key_id,
                            'key_type': key_type,
                            'metadata': metadata,
                            'created_at': created_at,
                            'expires_at': expires_at
                        })
                    return keys
        except Exception:
            return []
    
    def cleanup_expired_keys(self) -> int:
        """清理过期密钥"""
        try:
            with self.lock:
                current_time = int(time.time())
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.execute('''
                        UPDATE keys SET is_active = 0 
                        WHERE expires_at IS NOT NULL AND expires_at < ? AND is_active = 1
                    ''', (current_time,))
                    return cursor.rowcount
        except Exception:
            return 0


class KeyManager:
    """综合密钥管理器"""

    def __init__(self, storage_path: str = "keystore.db"):
        self.storage = SecureKeyStorage(storage_path)
        self.symmetric_crypto = SymmetricCrypto()
        self.rsa_crypto = RSACrypto()
        self.ecc_crypto = ECCCrypto()
        self.key_rotation_interval = 86400  # 24小时

    def initialize(self, master_password: str) -> bool:
        """初始化密钥管理器"""
        if not self.storage.set_master_password(master_password):
            # 如果主密码已存在，尝试解锁
            return self.storage.unlock(master_password)
        return True

    def unlock(self, master_password: str) -> bool:
        """解锁密钥管理器"""
        return self.storage.unlock(master_password)

    def generate_symmetric_key(self, key_id: str, expires_in: Optional[int] = None) -> bool:
        """生成对称密钥"""
        key = self.symmetric_crypto.generate_key()
        expires_at = int(time.time()) + expires_in if expires_in else None

        metadata = {
            'algorithm': 'AES-256-GCM',
            'key_size': len(key),
            'purpose': 'symmetric_encryption'
        }

        return self.storage.store_key(key_id, key, 'symmetric', metadata, expires_at)

    def generate_rsa_keypair(self, key_id: str, key_size: int = 4096,
                           expires_in: Optional[int] = None) -> bool:
        """生成RSA密钥对"""
        private_key, public_key = self.rsa_crypto.generate_keypair()
        expires_at = int(time.time()) + expires_in if expires_in else None

        # 序列化密钥
        private_pem = self.rsa_crypto.serialize_private_key(private_key)
        public_pem = self.rsa_crypto.serialize_public_key(public_key)

        # 存储私钥
        private_metadata = {
            'algorithm': 'RSA',
            'key_size': key_size,
            'purpose': 'asymmetric_private',
            'public_key_id': f"{key_id}_public"
        }

        # 存储公钥
        public_metadata = {
            'algorithm': 'RSA',
            'key_size': key_size,
            'purpose': 'asymmetric_public',
            'private_key_id': f"{key_id}_private"
        }

        private_stored = self.storage.store_key(
            f"{key_id}_private", private_pem, 'rsa_private', private_metadata, expires_at
        )
        public_stored = self.storage.store_key(
            f"{key_id}_public", public_pem, 'rsa_public', public_metadata, expires_at
        )

        return private_stored and public_stored

    def generate_ecc_keypair(self, key_id: str, expires_in: Optional[int] = None) -> bool:
        """生成ECC密钥对"""
        private_key, public_key = self.ecc_crypto.generate_keypair()
        expires_at = int(time.time()) + expires_in if expires_in else None

        # 序列化密钥
        private_pem = self.ecc_crypto.serialize_private_key(private_key)
        public_pem = self.ecc_crypto.serialize_public_key(public_key)

        # 存储私钥
        private_metadata = {
            'algorithm': 'ECC',
            'curve': 'SECP384R1',
            'purpose': 'asymmetric_private',
            'public_key_id': f"{key_id}_public"
        }

        # 存储公钥
        public_metadata = {
            'algorithm': 'ECC',
            'curve': 'SECP384R1',
            'purpose': 'asymmetric_public',
            'private_key_id': f"{key_id}_private"
        }

        private_stored = self.storage.store_key(
            f"{key_id}_private", private_pem, 'ecc_private', private_metadata, expires_at
        )
        public_stored = self.storage.store_key(
            f"{key_id}_public", public_pem, 'ecc_public', public_metadata, expires_at
        )

        return private_stored and public_stored

    def get_symmetric_key(self, key_id: str) -> Optional[bytes]:
        """获取对称密钥"""
        result = self.storage.retrieve_key(key_id)
        if result and result[1] == 'symmetric':
            return result[0]
        return None

    def get_rsa_private_key(self, key_id: str):
        """获取RSA私钥"""
        result = self.storage.retrieve_key(f"{key_id}_private")
        if result and result[1] == 'rsa_private':
            return self.rsa_crypto.load_private_key(result[0])
        return None

    def get_rsa_public_key(self, key_id: str):
        """获取RSA公钥"""
        result = self.storage.retrieve_key(f"{key_id}_public")
        if result and result[1] == 'rsa_public':
            return self.rsa_crypto.load_public_key(result[0])
        return None

    def get_ecc_private_key(self, key_id: str):
        """获取ECC私钥"""
        result = self.storage.retrieve_key(f"{key_id}_private")
        if result and result[1] == 'ecc_private':
            return self.ecc_crypto.load_private_key(result[0])
        return None

    def get_ecc_public_key(self, key_id: str):
        """获取ECC公钥"""
        result = self.storage.retrieve_key(f"{key_id}_public")
        if result and result[1] == 'ecc_public':
            return self.ecc_crypto.load_public_key(result[0])
        return None

    def rotate_key(self, key_id: str) -> bool:
        """轮换密钥"""
        # 获取现有密钥信息
        result = self.storage.retrieve_key(key_id)
        if not result:
            return False

        _, key_type, metadata = result

        # 根据密钥类型生成新密钥
        if key_type == 'symmetric':
            return self.generate_symmetric_key(key_id)
        elif key_type == 'rsa_private':
            base_key_id = key_id.replace('_private', '')
            key_size = metadata.get('key_size', 4096)
            return self.generate_rsa_keypair(base_key_id, key_size)
        elif key_type == 'ecc_private':
            base_key_id = key_id.replace('_private', '')
            return self.generate_ecc_keypair(base_key_id)

        return False

    def export_public_key(self, key_id: str) -> Optional[str]:
        """导出公钥（Base64编码）"""
        # 尝试RSA公钥
        result = self.storage.retrieve_key(f"{key_id}_public")
        if result and result[1] in ['rsa_public', 'ecc_public']:
            return base64.b64encode(result[0]).decode('ascii')
        return None

    def import_public_key(self, key_id: str, public_key_pem: str, algorithm: str) -> bool:
        """导入公钥"""
        try:
            public_key_bytes = base64.b64decode(public_key_pem)

            metadata = {
                'algorithm': algorithm,
                'purpose': 'asymmetric_public',
                'imported': True
            }

            key_type = f"{algorithm.lower()}_public"
            return self.storage.store_key(f"{key_id}_public", public_key_bytes, key_type, metadata)
        except Exception:
            return False

    def list_all_keys(self) -> Dict[str, List[Dict]]:
        """列出所有密钥"""
        all_keys = self.storage.list_keys()
        categorized = {
            'symmetric': [],
            'rsa': [],
            'ecc': []
        }

        for key_info in all_keys:
            key_type = key_info['key_type']
            if key_type == 'symmetric':
                categorized['symmetric'].append(key_info)
            elif key_type.startswith('rsa'):
                categorized['rsa'].append(key_info)
            elif key_type.startswith('ecc'):
                categorized['ecc'].append(key_info)

        return categorized

    def cleanup_expired(self) -> int:
        """清理过期密钥"""
        return self.storage.cleanup_expired_keys()

    def backup_keys(self, backup_path: str, backup_password: str) -> bool:
        """备份密钥"""
        try:
            all_keys = self.storage.list_keys()
            backup_data = {
                'timestamp': int(time.time()),
                'keys': []
            }

            for key_info in all_keys:
                key_result = self.storage.retrieve_key(key_info['key_id'])
                if key_result:
                    key_data, key_type, metadata = key_result
                    backup_data['keys'].append({
                        'key_id': key_info['key_id'],
                        'key_type': key_type,
                        'key_data': base64.b64encode(key_data).decode('ascii'),
                        'metadata': metadata,
                        'created_at': key_info['created_at'],
                        'expires_at': key_info['expires_at']
                    })

            # 加密备份数据
            backup_json = json.dumps(backup_data)
            backup_key, salt = KeyDerivation.pbkdf2(backup_password)

            from cryptography.fernet import Fernet
            fernet = Fernet(base64.urlsafe_b64encode(backup_key))
            encrypted_backup = fernet.encrypt(backup_json.encode())

            # 保存备份文件
            with open(backup_path, 'wb') as f:
                f.write(salt + encrypted_backup)

            return True
        except Exception:
            return False


# 示例使用
if __name__ == "__main__":
    print("=== 密钥管理系统示例 ===")

    # 创建密钥管理器
    key_manager = KeyManager("test_keystore.db")

    # 初始化（设置主密码）
    master_password = "my_very_secure_master_password_123!"
    if key_manager.initialize(master_password):
        print("密钥管理器初始化成功")
    else:
        print("密钥管理器初始化失败")
        exit(1)

    # 生成各种类型的密钥
    print("\n--- 生成密钥 ---")

    # 生成对称密钥
    if key_manager.generate_symmetric_key("session_key_001", expires_in=3600):
        print("✓ 对称密钥生成成功")

    # 生成RSA密钥对
    if key_manager.generate_rsa_keypair("user_rsa_001", expires_in=86400):
        print("✓ RSA密钥对生成成功")

    # 生成ECC密钥对
    if key_manager.generate_ecc_keypair("user_ecc_001", expires_in=86400):
        print("✓ ECC密钥对生成成功")

    # 获取和使用密钥
    print("\n--- 使用密钥 ---")

    # 获取对称密钥并加密消息
    sym_key = key_manager.get_symmetric_key("session_key_001")
    if sym_key:
        message = "这是一条测试消息"
        encrypted = key_manager.symmetric_crypto.encrypt_message(message, sym_key)
        decrypted = key_manager.symmetric_crypto.decrypt_message(encrypted, sym_key)
        print(f"对称加密测试: {message} -> {decrypted}")

    # 获取RSA密钥并签名
    rsa_private = key_manager.get_rsa_private_key("user_rsa_001")
    rsa_public = key_manager.get_rsa_public_key("user_rsa_001")
    if rsa_private and rsa_public:
        test_data = b"Test data for RSA signature"
        signature = key_manager.rsa_crypto.sign(test_data, rsa_private)
        is_valid = key_manager.rsa_crypto.verify(test_data, signature, rsa_public)
        print(f"RSA签名验证: {is_valid}")

    # 列出所有密钥
    print("\n--- 密钥列表 ---")
    all_keys = key_manager.list_all_keys()
    for category, keys in all_keys.items():
        if keys:
            print(f"{category.upper()}密钥:")
            for key_info in keys:
                print(f"  - {key_info['key_id']} ({key_info['metadata'].get('algorithm', 'Unknown')})")

    # 导出公钥
    print("\n--- 公钥导出 ---")
    rsa_public_pem = key_manager.export_public_key("user_rsa_001")
    if rsa_public_pem:
        print(f"RSA公钥已导出 (长度: {len(rsa_public_pem)})")

    ecc_public_pem = key_manager.export_public_key("user_ecc_001")
    if ecc_public_pem:
        print(f"ECC公钥已导出 (长度: {len(ecc_public_pem)})")

    # 备份密钥
    print("\n--- 密钥备份 ---")
    backup_password = "backup_password_456"
    if key_manager.backup_keys("keys_backup.enc", backup_password):
        print("✓ 密钥备份成功")
    else:
        print("✗ 密钥备份失败")

    # 清理过期密钥
    print("\n--- 清理过期密钥 ---")
    expired_count = key_manager.cleanup_expired()
    print(f"清理了 {expired_count} 个过期密钥")

    print("\n=== 密钥管理系统示例完成 ===")
