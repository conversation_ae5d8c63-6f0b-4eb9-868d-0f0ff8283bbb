"""
辅助函数模块
提供各种实用的辅助函数
"""

import os
import sys
import time
import hashlib
import secrets
import platform
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import base64
import json


def generate_unique_id(prefix: str = "") -> str:
    """生成唯一ID"""
    timestamp = int(time.time() * 1000)
    random_part = secrets.token_hex(8)
    return f"{prefix}{timestamp}_{random_part}" if prefix else f"{timestamp}_{random_part}"


def format_bytes(bytes_count: int) -> str:
    """格式化字节数"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_count < 1024:
            return f"{bytes_count:.1f} {unit}"
        bytes_count /= 1024
    return f"{bytes_count:.1f} PB"


def format_duration(seconds: float) -> str:
    """格式化时间间隔"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}小时"
    else:
        days = seconds / 86400
        return f"{days:.1f}天"


def format_timestamp(timestamp: Union[int, float, str], format_type: str = "datetime") -> str:
    """格式化时间戳"""
    try:
        if isinstance(timestamp, str):
            dt = datetime.fromisoformat(timestamp)
        else:
            dt = datetime.fromtimestamp(timestamp)
        
        if format_type == "time":
            return dt.strftime("%H:%M:%S")
        elif format_type == "date":
            return dt.strftime("%Y-%m-%d")
        elif format_type == "datetime":
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        elif format_type == "iso":
            return dt.isoformat()
        else:
            return dt.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        return str(timestamp)


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """安全的JSON解析"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """安全的JSON序列化"""
    try:
        return json.dumps(obj, ensure_ascii=False, separators=(',', ':'))
    except (TypeError, ValueError):
        return default


def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> str:
    """计算文件哈希值"""
    hash_func = getattr(hashlib, algorithm.lower())()
    
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except Exception:
        return ""


def verify_file_integrity(file_path: str, expected_hash: str, algorithm: str = "sha256") -> bool:
    """验证文件完整性"""
    actual_hash = calculate_file_hash(file_path, algorithm)
    return actual_hash.lower() == expected_hash.lower()


def secure_random_string(length: int, charset: str = None) -> str:
    """生成安全随机字符串"""
    if charset is None:
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    
    return ''.join(secrets.choice(charset) for _ in range(length))


def encode_base64(data: Union[str, bytes]) -> str:
    """Base64编码"""
    if isinstance(data, str):
        data = data.encode('utf-8')
    return base64.b64encode(data).decode('ascii')


def decode_base64(encoded_data: str) -> bytes:
    """Base64解码"""
    try:
        return base64.b64decode(encoded_data)
    except Exception:
        return b""


def sanitize_filename(filename: str) -> str:
    """清理文件名"""
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # 移除前后空格和点
    filename = filename.strip(' .')
    
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename or "unnamed"


def validate_ip_address(ip: str) -> bool:
    """验证IP地址"""
    import ipaddress
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False


def validate_port(port: Union[int, str]) -> bool:
    """验证端口号"""
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except (ValueError, TypeError):
        return False


def validate_url(url: str) -> bool:
    """验证URL"""
    import re
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    return url_pattern.match(url) is not None


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    import psutil
    
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'platform': platform.platform(),
            'python_version': sys.version,
            'cpu_count': psutil.cpu_count(),
            'cpu_percent': cpu_percent,
            'memory_total': memory.total,
            'memory_available': memory.available,
            'memory_percent': memory.percent,
            'disk_total': disk.total,
            'disk_free': disk.free,
            'disk_percent': (disk.used / disk.total) * 100
        }
    except ImportError:
        return {
            'platform': platform.platform(),
            'python_version': sys.version,
            'cpu_count': os.cpu_count(),
            'memory_total': 0,
            'memory_available': 0,
            'memory_percent': 0,
            'disk_total': 0,
            'disk_free': 0,
            'disk_percent': 0
        }


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (backoff ** attempt))
                    else:
                        raise last_exception
            
            return None
        return wrapper
    return decorator


def timeout_handler(timeout_seconds: float):
    """超时装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_signal_handler(signum, frame):
                raise TimeoutError(f"函数 {func.__name__} 执行超时")
            
            # 设置信号处理器
            old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
            signal.alarm(int(timeout_seconds))
            
            try:
                result = func(*args, **kwargs)
                signal.alarm(0)  # 取消定时器
                return result
            except TimeoutError:
                raise
            finally:
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator


def chunk_data(data: bytes, chunk_size: int) -> List[bytes]:
    """将数据分块"""
    chunks = []
    for i in range(0, len(data), chunk_size):
        chunks.append(data[i:i + chunk_size])
    return chunks


def merge_chunks(chunks: List[bytes]) -> bytes:
    """合并数据块"""
    return b''.join(chunks)


def calculate_checksum(data: bytes, algorithm: str = "md5") -> str:
    """计算数据校验和"""
    hash_func = getattr(hashlib, algorithm.lower())()
    hash_func.update(data)
    return hash_func.hexdigest()


def verify_checksum(data: bytes, expected_checksum: str, algorithm: str = "md5") -> bool:
    """验证数据校验和"""
    actual_checksum = calculate_checksum(data, algorithm)
    return actual_checksum.lower() == expected_checksum.lower()


def create_directory_if_not_exists(directory: str) -> bool:
    """创建目录（如果不存在）"""
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception:
        return False


def is_file_locked(file_path: str) -> bool:
    """检查文件是否被锁定"""
    try:
        with open(file_path, 'a'):
            return False
    except IOError:
        return True


def get_file_size(file_path: str) -> int:
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except Exception:
        return 0


def get_file_modification_time(file_path: str) -> float:
    """获取文件修改时间"""
    try:
        return os.path.getmtime(file_path)
    except Exception:
        return 0


def is_port_available(host: str, port: int) -> bool:
    """检查端口是否可用"""
    import socket
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            return result != 0
    except Exception:
        return False


def find_available_port(start_port: int = 8000, end_port: int = 9000, host: str = "localhost") -> Optional[int]:
    """查找可用端口"""
    for port in range(start_port, end_port + 1):
        if is_port_available(host, port):
            return port
    return None


def parse_connection_string(connection_string: str) -> Dict[str, Any]:
    """解析连接字符串"""
    # 支持格式: protocol://host:port/path
    import re
    
    pattern = r'^(?P<protocol>\w+)://(?P<host>[^:]+):(?P<port>\d+)(?P<path>/.*)?$'
    match = re.match(pattern, connection_string)
    
    if match:
        result = match.groupdict()
        result['port'] = int(result['port'])
        return result
    else:
        return {}


# 示例使用
if __name__ == "__main__":
    # 测试各种辅助函数
    print("=== 辅助函数测试 ===")
    
    # 生成唯一ID
    print(f"唯一ID: {generate_unique_id('test_')}")
    
    # 格式化
    print(f"格式化字节: {format_bytes(1536)}")
    print(f"格式化时间: {format_duration(3661)}")
    print(f"格式化时间戳: {format_timestamp(time.time())}")
    
    # 随机字符串
    print(f"随机字符串: {secure_random_string(16)}")
    
    # Base64编码
    test_data = "测试数据"
    encoded = encode_base64(test_data)
    decoded = decode_base64(encoded).decode('utf-8')
    print(f"Base64编码: {encoded}")
    print(f"Base64解码: {decoded}")
    
    # 文件名清理
    unsafe_filename = "test<>file|name?.txt"
    safe_filename = sanitize_filename(unsafe_filename)
    print(f"清理文件名: {unsafe_filename} -> {safe_filename}")
    
    # 验证
    print(f"验证IP: {validate_ip_address('***********')}")
    print(f"验证端口: {validate_port(8080)}")
    print(f"验证URL: {validate_url('https://example.com:8080/path')}")
    
    # 系统信息
    sys_info = get_system_info()
    print(f"系统信息: {sys_info['platform']}")
    
    # 查找可用端口
    available_port = find_available_port(8000, 8010)
    print(f"可用端口: {available_port}")
    
    # 解析连接字符串
    conn_info = parse_connection_string("ws://localhost:8001/chat")
    print(f"连接信息: {conn_info}")
    
    print("=== 测试完成 ===")
