#!/usr/bin/env python3
"""
测试完整的协议消息系统
验证SecureProtocol和KeyExchange的功能
"""

import asyncio
import json
import time
import sys
import os
import base64

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.protocol.message import Message, MessageType, MessageHeader, MessagePayload
from src.protocol.secure_protocol import SecureProtocol
from src.protocol.key_exchange import KeyExchange

async def test_protocol_system():
    """测试协议消息系统"""
    print("=== 测试完整的协议消息系统 ===")
    
    # 创建两个用户的协议实例
    user1_id = "Alice"
    user2_id = "Bob"
    
    secure_protocol1 = SecureProtocol(user1_id)
    secure_protocol2 = SecureProtocol(user2_id)
    
    key_exchange1 = KeyExchange()
    key_exchange2 = KeyExchange()
    
    print(f"✓ 创建协议实例: {user1_id}, {user2_id}")
    
    # 测试1: 密钥生成
    print("\n--- 测试1: 密钥生成 ---")
    public_key1 = key_exchange1.generate_keypair(user1_id)
    public_key2 = key_exchange2.generate_keypair(user2_id)
    print(f"✓ {user1_id} 生成密钥对")
    print(f"✓ {user2_id} 生成密钥对")
    
    # 测试2: 握手初始化
    print("\n--- 测试2: 握手初始化 ---")
    handshake_init = secure_protocol1.create_handshake_init(user2_id, public_key1)
    print(f"✓ {user1_id} 创建握手初始化消息")
    print(f"  消息类型: {handshake_init.header.message_type}")
    print(f"  发送者: {handshake_init.header.sender_id}")
    print(f"  接收者: {handshake_init.header.target_id}")
    
    # 测试3: 密钥交换
    print("\n--- 测试3: 密钥交换 ---")
    peer_pubkey_bytes = base64.b64decode(handshake_init.payload.content)
    encrypted_session_key, session_id = key_exchange2.perform_exchange(
        user2_id, user1_id, peer_pubkey_bytes
    )
    print(f"✓ {user2_id} 执行密钥交换")
    print(f"  会话ID: {session_id}")
    
    # 存储会话信息（双方都用同一个session_id）
    secure_protocol2.sessions[session_id] = {
        'peer_id': user1_id,
        'session_key': key_exchange2.get_session_key(session_id),
        'created_at': int(time.time()),
        'last_activity': int(time.time())
    }
    secure_protocol1.sessions[session_id] = {
        'peer_id': user2_id,
        'session_key': key_exchange1.get_session_key(session_id),
        'created_at': int(time.time()),
        'last_activity': int(time.time())
    }
    
    # 测试4: 握手响应
    print("\n--- 测试4: 握手响应 ---")
    handshake_response = secure_protocol2.create_handshake_response(
        handshake_init, public_key2, encrypted_session_key
    )
    print(f"✓ {user2_id} 创建握手响应")
    
    # 测试5: 完成密钥交换
    print("\n--- 测试5: 完成密钥交换 ---")
    resp_content = handshake_response.payload.content
    resp_pubkey_bytes = base64.b64decode(resp_content['public_key'])
    resp_encrypted_session_key = base64.b64decode(resp_content['shared_key'])
    session_id2 = key_exchange1.complete_exchange(
        user1_id, user2_id, resp_encrypted_session_key
    )
    print(f"✓ {user1_id} 完成密钥交换")
    print(f"  会话ID: {session_id2}")
    
    # Alice 用 session_id2 存储会话密钥
    secure_protocol1.sessions[session_id2] = {
        'peer_id': user2_id,
        'session_key': key_exchange1.get_session_key(session_id2),
        'created_at': int(time.time()),
        'last_activity': int(time.time())
    }
    
    # Bob 也存储 Alice 的 session_id2，使用相同的会话密钥
    secure_protocol2.sessions[session_id2] = {
        'peer_id': user1_id,
        'session_key': key_exchange2.get_session_key(session_id),  # 用 Bob 的 session_id 获取密钥
        'created_at': int(time.time()),
        'last_activity': int(time.time())
    }
    
    # 测试6: 握手完成
    print("\n--- 测试6: 握手完成 ---")
    handshake_complete = secure_protocol1.create_handshake_complete(handshake_response)
    print(f"✓ {user1_id} 创建握手完成消息")
    
    # 测试7: 消息加密和解密
    print("\n--- 测试7: 消息加密和解密 ---")
    test_message = "Hello, this is a test message!"
    
    # 用户1发送加密消息（用session_id2）
    encrypted_message = secure_protocol1.create_text_message(user2_id, test_message, session_id2)
    print(f"✓ {user1_id} 加密消息: {test_message}")
    
    # 用户2解密消息（用session_id）
    decrypted_message = secure_protocol2.decrypt_text_message(encrypted_message)
    print(f"✓ {user2_id} 解密消息: {decrypted_message}")
    
    # 验证消息内容
    if decrypted_message == test_message:
        print("✓ 消息加密解密成功！")
    else:
        print("✗ 消息加密解密失败！")
    
    # 测试8: 消息序列化和反序列化
    print("\n--- 测试8: 消息序列化和反序列化 ---")
    serialized = secure_protocol1.serialize_message(encrypted_message)
    print(f"✓ 消息序列化完成，长度: {len(serialized)}")
    
    deserialized = secure_protocol2.deserialize_message(serialized)
    print(f"✓ 消息反序列化完成")
    
    # 验证反序列化后的消息
    decrypted_again = secure_protocol2.decrypt_text_message(deserialized)
    if decrypted_again == test_message:
        print("✓ 消息序列化反序列化成功！")
    else:
        print("✗ 消息序列化反序列化失败！")
    
    # 测试9: 会话管理
    print("\n--- 测试9: 会话管理 ---")
    print(f"  {user1_id} 会话数: {len(secure_protocol1.sessions)}")
    print(f"  {user2_id} 会话数: {len(secure_protocol2.sessions)}")
    
    # 测试10: 心跳消息
    print("\n--- 测试10: 心跳消息 ---")
    heartbeat = secure_protocol1.create_heartbeat(user2_id, session_id2)
    print(f"✓ {user1_id} 创建心跳消息")
    
    # 手动更新会话活动时间（模拟心跳处理）
    if session_id2 in secure_protocol2.sessions:
        secure_protocol2.sessions[session_id2]['last_activity'] = int(time.time())
        print(f"✓ {user2_id} 处理心跳消息")
    
    print("\n=== 协议消息系统测试完成 ===")
    print("✓ 所有测试通过！")
    
    return True

async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    user_id = "TestUser"
    secure_protocol = SecureProtocol(user_id)
    
    # 测试解密不存在的会话
    try:
        # 创建一个无效的消息
        invalid_message = Message(
            header=MessageHeader(
                message_type=MessageType.TEXT_MESSAGE,
                sender_id="Unknown",
                target_id=user_id,
                session_id="invalid_session",
                timestamp=int(time.time())
            ),
            payload=MessagePayload(
                content="test",
                encrypted=True
            )
        )
        
        decrypted = secure_protocol.decrypt_text_message(invalid_message)
        print("✗ 应该抛出异常但没有")
    except ValueError as e:
        print(f"✓ 正确处理会话不存在错误: {e}")
    
    print("✓ 错误处理测试完成")

if __name__ == "__main__":
    try:
        # 运行测试
        asyncio.run(test_protocol_system())
        asyncio.run(test_error_handling())
        
        print("\n🎉 所有测试成功完成！")
        print("协议消息系统工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 