"""
消息协议定义
包含消息类型、消息头和消息体的定义
"""

import json
import time
import base64
from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass


class MessageType(Enum):
    """消息类型枚举"""
    HANDSHAKE_INIT = "handshake_init"
    HANDSHAKE_RESPONSE = "handshake_response"
    HANDSHAKE_COMPLETE = "handshake_complete"
    TEXT_MESSAGE = "text_message"
    FILE_TRANSFER_INIT = "file_transfer_init"
    FILE_CHUNK = "file_chunk"
    HEARTBEAT = "heartbeat"
    ERROR = "error"


@dataclass
class MessageHeader:
    """消息头"""
    message_type: MessageType
    sender_id: str
    target_id: str
    timestamp: int
    session_id: Optional[str] = None
    chunk_index: Optional[int] = None
    total_chunks: Optional[int] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            'message_type': self.message_type.value,
            'sender_id': self.sender_id,
            'target_id': self.target_id,
            'timestamp': self.timestamp
        }
        if self.session_id:
            data['session_id'] = self.session_id
        if self.chunk_index is not None:
            data['chunk_index'] = self.chunk_index
        if self.total_chunks is not None:
            data['total_chunks'] = self.total_chunks
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessageHeader':
        """从字典创建"""
        return cls(
            message_type=MessageType(data['message_type']),
            sender_id=data['sender_id'],
            target_id=data['target_id'],
            timestamp=data['timestamp'],
            session_id=data.get('session_id'),
            chunk_index=data.get('chunk_index'),
            total_chunks=data.get('total_chunks')
        )


@dataclass
class MessagePayload:
    """消息体"""
    content: Any
    encrypted: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'content': self.content,
            'encrypted': self.encrypted
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MessagePayload':
        """从字典创建"""
        return cls(
            content=data['content'],
            encrypted=data.get('encrypted', False)
        )


class Message:
    """消息类"""
    def __init__(self, header: MessageHeader, payload: MessagePayload):
        self.header = header
        self.payload = payload

    def to_dict(self):
        return {
            'header': self.header.to_dict(),
            'payload': self.payload.to_dict()
        }

    @classmethod
    def from_dict(cls, d: dict) -> 'Message':
        header = MessageHeader.from_dict(d['header'])
        payload = MessagePayload.from_dict(d['payload'])
        return cls(header, payload)

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> 'Message':
        """从JSON字符串创建消息"""
        data = json.loads(json_str)
        return cls.from_dict(data)

    @classmethod
    def create_handshake_init(cls, sender_id: str, target_id: str, public_key: bytes) -> 'Message':
        """创建握手初始化消息"""
        header = MessageHeader(
            message_type=MessageType.HANDSHAKE_INIT,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time())
        )
        payload = MessagePayload(
            content=base64.b64encode(public_key).decode('ascii'),
            encrypted=False
        )
        return cls(header, payload)

    @classmethod
    def create_handshake_response(cls, sender_id: str, target_id: str, public_key: bytes, shared_key: bytes, session_id: str) -> 'Message':
        """创建握手响应消息"""
        header = MessageHeader(
            message_type=MessageType.HANDSHAKE_RESPONSE,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time()),
            session_id=session_id
        )
        payload = MessagePayload(
            content={
                'public_key': base64.b64encode(public_key).decode('ascii'),
                'shared_key': base64.b64encode(shared_key).decode('ascii'),
                'session_id': session_id
            },
            encrypted=False
        )
        return cls(header, payload)

    @classmethod
    def create_handshake_complete(cls, sender_id: str, target_id: str, session_id: str) -> 'Message':
        """创建握手完成消息"""
        header = MessageHeader(
            message_type=MessageType.HANDSHAKE_COMPLETE,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time()),
            session_id=session_id
        )
        payload = MessagePayload(
            content={'status': 'success'},
            encrypted=False
        )
        return cls(header, payload)

    @classmethod
    def create_text_message(cls, sender_id: str, target_id: str, text: str, session_id: str) -> 'Message':
        """创建文本消息"""
        header = MessageHeader(
            message_type=MessageType.TEXT_MESSAGE,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time()),
            session_id=session_id
        )
        payload = MessagePayload(
            content=text,
            encrypted=True
        )
        return cls(header, payload)

    @classmethod
    def create_file_transfer_init(cls, sender_id: str, target_id: str, filename: str, file_size: int, file_hash: str, session_id: str) -> 'Message':
        """创建文件传输初始化消息"""
        header = MessageHeader(
            message_type=MessageType.FILE_TRANSFER_INIT,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time()),
            session_id=session_id
        )
        payload = MessagePayload(
            content={
                'filename': filename,
                'file_size': file_size,
                'file_hash': file_hash
            },
            encrypted=True
        )
        return cls(header, payload)

    @classmethod
    def create_file_chunk(cls, sender_id: str, target_id: str, chunk_data: bytes, chunk_index: int, total_chunks: int, session_id: str) -> 'Message':
        """创建文件块消息"""
        header = MessageHeader(
            message_type=MessageType.FILE_CHUNK,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time()),
            session_id=session_id,
            chunk_index=chunk_index,
            total_chunks=total_chunks
        )
        payload = MessagePayload(
            content=base64.b64encode(chunk_data).decode('ascii'),
            encrypted=True
        )
        return cls(header, payload)

    @classmethod
    def create_heartbeat(cls, sender_id: str, target_id: str, session_id: str) -> 'Message':
        """创建心跳消息"""
        header = MessageHeader(
            message_type=MessageType.HEARTBEAT,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time()),
            session_id=session_id
        )
        payload = MessagePayload(
            content={'timestamp': int(time.time())},
            encrypted=False
        )
        return cls(header, payload)

    @classmethod
    def create_error(cls, sender_id: str, target_id: str, error_code: str, error_message: str) -> 'Message':
        """创建错误消息"""
        header = MessageHeader(
            message_type=MessageType.ERROR,
            sender_id=sender_id,
            target_id=target_id,
            timestamp=int(time.time())
        )
        payload = MessagePayload(
            content={
                'error_code': error_code,
                'error_message': error_message
            },
            encrypted=False
        )
        return cls(header, payload)