"""
日志系统模块
提供统一的日志记录功能
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class SecurityLogger:
    """安全日志记录器"""
    
    def __init__(self, name: str = "anonymous_chat", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 普通日志
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.name}.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 安全事件处理器
        security_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.name}_security.log",
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        security_handler.setLevel(logging.WARNING)
        security_formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        )
        security_handler.setFormatter(security_formatter)
        self.logger.addHandler(security_handler)
        
        # 错误处理器
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.name}_error.log",
            maxBytes=20*1024*1024,  # 20MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = logging.Formatter(
            '%(asctime)s - ERROR - %(name)s - %(funcName)s:%(lineno)d - %(message)s\n%(exc_info)s'
        )
        error_handler.setFormatter(error_formatter)
        self.logger.addHandler(error_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, **kwargs)
    
    def security_event(self, event_type: str, details: str, severity: str = "WARNING"):
        """记录安全事件"""
        message = f"[{event_type}] {details}"
        if severity == "CRITICAL":
            self.logger.critical(message)
        elif severity == "ERROR":
            self.logger.error(message)
        else:
            self.logger.warning(message)
    
    def connection_event(self, event: str, client_id: str, details: str = ""):
        """记录连接事件"""
        message = f"CONNECTION - {event} - Client: {client_id}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def crypto_event(self, operation: str, success: bool, details: str = ""):
        """记录加密操作事件"""
        status = "SUCCESS" if success else "FAILED"
        message = f"CRYPTO - {operation} - {status}"
        if details:
            message += f" - {details}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)
    
    def network_event(self, event: str, details: str = ""):
        """记录网络事件"""
        message = f"NETWORK - {event}"
        if details:
            message += f" - {details}"
        self.logger.info(message)


class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("audit")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            self._setup_audit_handler()
    
    def _setup_audit_handler(self):
        """设置审计日志处理器"""
        audit_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "audit.log",
            maxBytes=100*1024*1024,  # 100MB
            backupCount=20,
            encoding='utf-8'
        )
        audit_formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s'
        )
        audit_handler.setFormatter(audit_formatter)
        self.logger.addHandler(audit_handler)
    
    def log_user_action(self, user_id: str, action: str, details: str = ""):
        """记录用户操作"""
        message = f"USER:{user_id} - ACTION:{action}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_system_event(self, event: str, details: str = ""):
        """记录系统事件"""
        message = f"SYSTEM - {event}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_security_violation(self, violation_type: str, source: str, details: str = ""):
        """记录安全违规"""
        message = f"VIOLATION:{violation_type} - SOURCE:{source}"
        if details:
            message += f" - {details}"
        self.logger.warning(message)


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("performance")
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            self._setup_performance_handler()
    
    def _setup_performance_handler(self):
        """设置性能日志处理器"""
        perf_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "performance.log",
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        perf_formatter = logging.Formatter(
            '%(asctime)s - PERF - %(message)s'
        )
        perf_handler.setFormatter(perf_formatter)
        self.logger.addHandler(perf_handler)
    
    def log_operation_time(self, operation: str, duration: float, details: str = ""):
        """记录操作耗时"""
        message = f"OP:{operation} - TIME:{duration:.4f}s"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_throughput(self, operation: str, count: int, duration: float):
        """记录吞吐量"""
        throughput = count / duration if duration > 0 else 0
        message = f"THROUGHPUT:{operation} - {throughput:.2f} ops/s - COUNT:{count} - TIME:{duration:.4f}s"
        self.logger.info(message)
    
    def log_resource_usage(self, cpu_percent: float, memory_mb: float, details: str = ""):
        """记录资源使用情况"""
        message = f"RESOURCE - CPU:{cpu_percent:.1f}% - MEM:{memory_mb:.1f}MB"
        if details:
            message += f" - {details}"
        self.logger.info(message)


# 全局日志实例
security_logger = SecurityLogger()
audit_logger = AuditLogger()
performance_logger = PerformanceLogger()


def get_logger(name: str = "anonymous_chat") -> SecurityLogger:
    """获取日志记录器"""
    return SecurityLogger(name)


def setup_logging(level: str = "INFO", log_dir: str = "logs"):
    """设置全局日志"""
    global security_logger, audit_logger, performance_logger
    
    # 创建日志目录
    Path(log_dir).mkdir(exist_ok=True)
    
    # 设置日志级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # 重新初始化日志记录器
    security_logger = SecurityLogger("anonymous_chat", log_dir)
    security_logger.logger.setLevel(numeric_level)
    
    audit_logger = AuditLogger(log_dir)
    performance_logger = PerformanceLogger(log_dir)


def log_startup():
    """记录系统启动"""
    security_logger.info("=== 匿名加密通讯系统启动 ===")
    security_logger.info(f"Python版本: {sys.version}")
    security_logger.info(f"工作目录: {os.getcwd()}")
    audit_logger.log_system_event("SYSTEM_START")


def log_shutdown():
    """记录系统关闭"""
    security_logger.info("=== 匿名加密通讯系统关闭 ===")
    audit_logger.log_system_event("SYSTEM_STOP")


# 装饰器
def log_function_call(logger: Optional[SecurityLogger] = None):
    """函数调用日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            log = logger or security_logger
            func_name = func.__name__
            log.debug(f"调用函数: {func_name}")
            
            try:
                result = func(*args, **kwargs)
                log.debug(f"函数 {func_name} 执行成功")
                return result
            except Exception as e:
                log.error(f"函数 {func_name} 执行失败: {e}")
                raise
        return wrapper
    return decorator


def log_performance(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                performance_logger.log_operation_time(operation_name, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                performance_logger.log_operation_time(f"{operation_name}_FAILED", duration)
                raise
        return wrapper
    return decorator


# 示例使用
if __name__ == "__main__":
    # 设置日志
    setup_logging("DEBUG")
    
    # 记录启动
    log_startup()
    
    # 测试各种日志
    security_logger.info("这是一条信息日志")
    security_logger.warning("这是一条警告日志")
    security_logger.error("这是一条错误日志")
    
    security_logger.security_event("LOGIN_ATTEMPT", "用户尝试登录", "INFO")
    security_logger.connection_event("CONNECT", "client_001", "来自 192.168.1.100")
    security_logger.crypto_event("ENCRYPT", True, "AES-256加密成功")
    security_logger.network_event("MESSAGE_SENT", "发送到 relay_001")
    
    audit_logger.log_user_action("user_001", "SEND_MESSAGE", "发送给 user_002")
    audit_logger.log_system_event("CONFIG_CHANGED", "更新中继服务器列表")
    
    performance_logger.log_operation_time("ENCRYPT_MESSAGE", 0.0123)
    performance_logger.log_throughput("MESSAGE_PROCESSING", 1000, 10.5)
    
    # 记录关闭
    log_shutdown()
