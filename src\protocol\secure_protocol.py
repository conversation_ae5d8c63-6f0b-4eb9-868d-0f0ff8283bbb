"""
安全协议实现
提供端到端加密和消息处理功能
"""

import json
import time
import base64
import secrets
from typing import Dict, Optional, Any
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from .message import Message, MessageType, MessageHeader, MessagePayload
from cryptography.hazmat.primitives import padding as padding_primitives
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes


class SecureProtocol:
    """安全协议实现"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.sessions: Dict[str, Dict] = {}  # session_id -> session_info
        self.temp_keys: Dict[str, Any] = {}  # peer_id -> temp_key
        
    def create_handshake_init(self, peer_id: str, public_key: bytes) -> Message:
        """创建握手初始化消息"""
        return Message.create_handshake_init(self.user_id, peer_id, public_key)
    
    def create_handshake_response(self, message: Message, public_key: bytes, shared_key: bytes, session_id: str) -> Message:
        """创建握手响应消息"""
        return Message.create_handshake_response(self.user_id, message.header.sender_id, public_key, shared_key, session_id)
    
    def create_handshake_complete(self, message: Message, session_id: str = None) -> Message:
        """创建握手完成消息"""
        if session_id is None:
            session_id = message.header.session_id or f"session_{secrets.token_hex(8)}"
        return Message.create_handshake_complete(self.user_id, message.header.sender_id, session_id)
    
    def create_text_message(self, peer_id: str, text: str, session_id: str) -> Message:
        """创建加密文本消息"""
        # 获取会话密钥
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"会话不存在: {session_id}")
        
        # 使用AES-GCM加密消息
        session_key = session['session_key']
        aesgcm = AESGCM(session_key)
        nonce = secrets.token_bytes(12)
        ciphertext = aesgcm.encrypt(nonce, text.encode(), None)
        
        # 组合加密数据
        encrypted_data = {
            'nonce': base64.b64encode(nonce).decode('ascii'),
            'ciphertext': base64.b64encode(ciphertext).decode('ascii')
        }
        
        # 创建消息
        message = Message.create_text_message(self.user_id, peer_id, json.dumps(encrypted_data), session_id)
        return message
    
    def create_file_transfer_init(self, peer_id: str, filename: str, file_size: int, file_hash: str, session_id: str) -> Message:
        """创建文件传输初始化消息"""
        return Message.create_file_transfer_init(self.user_id, peer_id, filename, file_size, file_hash, session_id)
    
    def create_file_chunk(self, peer_id: str, transfer_id: str, chunk_data: bytes, chunk_index: int, total_chunks: int, session_id: str) -> Message:
        """创建加密文件块消息"""
        # 获取会话密钥
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"会话不存在: {session_id}")
        
        # 使用AES-GCM加密文件块
        session_key = session['session_key']
        aesgcm = AESGCM(session_key)
        nonce = secrets.token_bytes(12)
        ciphertext = aesgcm.encrypt(nonce, chunk_data, None)
        
        # 组合加密数据
        encrypted_data = {
            'transfer_id': transfer_id,
            'nonce': base64.b64encode(nonce).decode('ascii'),
            'ciphertext': base64.b64encode(ciphertext).decode('ascii')
        }
        
        # 创建消息
        message = Message.create_file_chunk(self.user_id, peer_id, json.dumps(encrypted_data).encode(), chunk_index, total_chunks, session_id)
        return message
    
    def create_heartbeat(self, peer_id: str, session_id: str) -> Message:
        """创建心跳消息"""
        return Message.create_heartbeat(self.user_id, peer_id, session_id)
    
    def create_error(self, peer_id: str, error_code: str, error_message: str) -> Message:
        """创建错误消息"""
        return Message.create_error(self.user_id, peer_id, error_code, error_message)
    
    def decrypt_text_message(self, message: Message) -> str:
        """解密文本消息"""
        if not message.payload.encrypted:
            return message.payload.content
        
        # 解析加密数据
        encrypted_data = json.loads(message.payload.content)
        nonce = base64.b64decode(encrypted_data['nonce'])
        ciphertext = base64.b64decode(encrypted_data['ciphertext'])
        
        # 获取会话密钥
        session_id = message.header.session_id
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"会话不存在: {session_id}")
        
        # 使用AES-GCM解密消息
        session_key = session['session_key']
        aesgcm = AESGCM(session_key)
        plaintext = aesgcm.decrypt(nonce, ciphertext, None)
        
        return plaintext.decode('utf-8')
    
    def decrypt_file_chunk(self, message: Message) -> bytes:
        """解密文件块"""
        if not message.payload.encrypted:
            return base64.b64decode(message.payload.content)
        
        # 解析加密数据
        encrypted_data = json.loads(message.payload.content)
        nonce = base64.b64decode(encrypted_data['nonce'])
        ciphertext = base64.b64decode(encrypted_data['ciphertext'])
        
        # 获取会话密钥
        session_id = message.header.session_id
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"会话不存在: {session_id}")
        
        # 使用AES-GCM解密文件块
        session_key = session['session_key']
        aesgcm = AESGCM(session_key)
        plaintext = aesgcm.decrypt(nonce, ciphertext, None)
        
        return plaintext
    
    def serialize_message(self, message: Message) -> str:
        """序列化消息"""
        return message.to_json()
    
    def deserialize_message(self, data: str) -> Message:
        """反序列化消息"""
        return Message.from_json(data)
    
    def cleanup_expired_sessions(self) -> int:
        """清理过期会话"""
        current_time = int(time.time())
        expired_count = 0
        
        for session_id, session in list(self.sessions.items()):
            if current_time - session['last_activity'] > 3600:  # 1小时过期
                del self.sessions[session_id]
                expired_count += 1
        
        return expired_count

    def encrypt_message(self, session_id: str, plaintext: bytes) -> str:
        key = self.get_session_key(session_id)
        if not key:
            raise ValueError("No session key for session_id")
        iv = secrets.token_bytes(16)
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        encryptor = cipher.encryptor()
        pad_len = 16 - (len(plaintext) % 16)
        padded = plaintext + bytes([pad_len] * pad_len)
        ciphertext = encryptor.update(padded) + encryptor.finalize()
        # base64编码iv和密文
        return base64.b64encode(iv + ciphertext).decode('ascii')

    def decrypt_message(self, session_id: str, ciphertext_b64: str) -> bytes:
        key = self.get_session_key(session_id)
        if not key:
            raise ValueError("No session key for session_id")
        data = base64.b64decode(ciphertext_b64)
        iv, ciphertext = data[:16], data[16:]
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded = decryptor.update(ciphertext) + decryptor.finalize()
        pad_len = padded[-1]
        return padded[:-pad_len] 