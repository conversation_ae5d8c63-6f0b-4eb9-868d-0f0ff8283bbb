#!/usr/bin/env python3
"""
简化的连接测试 - 验证WebSocket连接保持
"""

import asyncio
import websockets
import json
import time

async def test_connection():
    """测试WebSocket连接保持"""
    try:
        print("开始连接测试...")
        
        # 连接到第一个中继服务器
        uri = "ws://localhost:8011"
        print(f"连接到: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✓ WebSocket连接成功")
            
            # 发送注册消息
            register_msg = {
                "type": "register",
                "user_id": "test_user",
                "display_name": "Test User",
                "timestamp": int(time.time())
            }
            
            await websocket.send(json.dumps(register_msg))
            print("✓ 注册消息已发送")
            
            # 等待注册响应
            response = await websocket.recv()
            response_data = json.loads(response)
            print(f"✓ 收到响应: {response_data}")
            
            # 保持连接并发送心跳
            for i in range(20):  # 测试20次，每次3秒
                await asyncio.sleep(3)
                
                # 发送心跳
                ping_msg = {
                    "type": "ping",
                    "timestamp": int(time.time())
                }
                
                try:
                    await websocket.send(json.dumps(ping_msg))
                    print(f"✓ 心跳 {i+1}/20 已发送")
                    
                    # 尝试接收响应（非阻塞）
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"  收到响应: {response}")
                    except asyncio.TimeoutError:
                        print(f"  心跳 {i+1} 无响应（正常）")
                        
                except Exception as e:
                    print(f"❌ 心跳 {i+1} 发送失败: {e}")
                    break
            
            print("✓ 连接测试完成")
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_connection())
