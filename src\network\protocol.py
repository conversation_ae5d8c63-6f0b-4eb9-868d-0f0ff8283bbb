"""
安全通信协议
定义端到端加密通信的消息格式和协议流程
"""

import json
import time
import secrets
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
import base64
from ..crypto import SymmetricCrypto, MessageAuth, SecureHash


class MessageType(Enum):
    """消息类型枚举"""
    HANDSHAKE_INIT = "handshake_init"
    HANDSHAKE_RESPONSE = "handshake_response"
    HANDSHAKE_COMPLETE = "handshake_complete"
    KEY_EXCHANGE = "key_exchange"
    TEXT_MESSAGE = "text_message"
    FILE_TRANSFER_INIT = "file_transfer_init"
    FILE_CHUNK = "file_chunk"
    FILE_TRANSFER_COMPLETE = "file_transfer_complete"
    HEARTBEAT = "heartbeat"
    ERROR = "error"
    DISCONNECT = "disconnect"


@dataclass
class MessageHeader:
    """消息头部"""
    message_id: str
    message_type: MessageType
    sender_id: str
    receiver_id: str
    timestamp: int
    sequence_number: int
    total_chunks: int = 1
    chunk_index: int = 0
    session_id: Optional[str] = None


@dataclass
class ProtocolMessage:
    """协议消息"""
    header: MessageHeader
    payload: Dict[str, Any]
    signature: Optional[str] = None
    mac: Optional[str] = None


class SecureProtocol:
    """安全通信协议"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.crypto = SymmetricCrypto()
        self.auth = MessageAuth()
        self.sequence_counter = 0
        self.sessions: Dict[str, Dict] = {}
        self.message_cache: Dict[str, ProtocolMessage] = {}
        
    def generate_message_id(self) -> str:
        """生成消息ID"""
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        random_part = secrets.token_hex(8)
        return f"{timestamp}_{random_part}"
    
    def create_message_header(self, message_type: MessageType, receiver_id: str,
                            session_id: Optional[str] = None, total_chunks: int = 1,
                            chunk_index: int = 0) -> MessageHeader:
        """创建消息头部"""
        self.sequence_counter += 1
        return MessageHeader(
            message_id=self.generate_message_id(),
            message_type=message_type,
            sender_id=self.user_id,
            receiver_id=receiver_id,
            timestamp=int(time.time()),
            sequence_number=self.sequence_counter,
            total_chunks=total_chunks,
            chunk_index=chunk_index,
            session_id=session_id
        )
    
    def create_handshake_init(self, receiver_id: str, public_key: bytes) -> ProtocolMessage:
        """创建握手初始化消息"""
        header = self.create_message_header(MessageType.HANDSHAKE_INIT, receiver_id)
        
        payload = {
            'public_key': base64.b64encode(public_key).decode('ascii'),
            'supported_algorithms': ['AES-256-GCM', 'RSA-4096', 'ECDH-P384'],
            'protocol_version': '1.0'
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_handshake_response(self, init_message: ProtocolMessage, 
                                public_key: bytes, session_key: bytes) -> ProtocolMessage:
        """创建握手响应消息"""
        header = self.create_message_header(
            MessageType.HANDSHAKE_RESPONSE, 
            init_message.header.sender_id
        )
        
        # 生成会话ID
        session_id = self.generate_session_id(init_message.header.sender_id)
        header.session_id = session_id
        
        payload = {
            'public_key': base64.b64encode(public_key).decode('ascii'),
            'session_id': session_id,
            'selected_algorithms': ['AES-256-GCM', 'ECDH-P384']
        }
        
        # 存储会话信息
        self.sessions[session_id] = {
            'peer_id': init_message.header.sender_id,
            'session_key': session_key,
            'created_at': int(time.time()),
            'last_activity': int(time.time())
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_handshake_complete(self, response_message: ProtocolMessage) -> ProtocolMessage:
        """创建握手完成消息"""
        header = self.create_message_header(
            MessageType.HANDSHAKE_COMPLETE,
            response_message.header.sender_id
        )
        header.session_id = response_message.header.session_id
        
        payload = {
            'status': 'success',
            'message': 'Handshake completed successfully'
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_text_message(self, receiver_id: str, text: str, session_id: str) -> ProtocolMessage:
        """创建文本消息"""
        if session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        header = self.create_message_header(MessageType.TEXT_MESSAGE, receiver_id, session_id)
        
        # 加密消息内容
        session_key = self.sessions[session_id]['session_key']
        encrypted_text = self.crypto.encrypt_message(text, session_key, self.user_id, receiver_id)
        
        payload = {
            'encrypted_content': encrypted_text,
            'content_type': 'text/plain'
        }
        
        # 更新会话活动时间
        self.sessions[session_id]['last_activity'] = int(time.time())
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_file_transfer_init(self, receiver_id: str, filename: str, 
                                file_size: int, file_hash: str, session_id: str) -> ProtocolMessage:
        """创建文件传输初始化消息"""
        if session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        header = self.create_message_header(MessageType.FILE_TRANSFER_INIT, receiver_id, session_id)
        
        payload = {
            'filename': filename,
            'file_size': file_size,
            'file_hash': file_hash,
            'chunk_size': 64 * 1024,  # 64KB chunks
            'transfer_id': self.generate_message_id()
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_file_chunk(self, receiver_id: str, transfer_id: str, chunk_data: bytes,
                         chunk_index: int, total_chunks: int, session_id: str) -> ProtocolMessage:
        """创建文件块消息"""
        if session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        header = self.create_message_header(
            MessageType.FILE_CHUNK, receiver_id, session_id, total_chunks, chunk_index
        )
        
        # 加密文件块
        session_key = self.sessions[session_id]['session_key']
        encrypted_chunk, nonce = self.crypto.encrypt(chunk_data, session_key)
        
        payload = {
            'transfer_id': transfer_id,
            'encrypted_chunk': base64.b64encode(encrypted_chunk).decode('ascii'),
            'nonce': base64.b64encode(nonce).decode('ascii'),
            'chunk_hash': SecureHash.sha256_hex(chunk_data)
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_heartbeat(self, receiver_id: str, session_id: str) -> ProtocolMessage:
        """创建心跳消息"""
        header = self.create_message_header(MessageType.HEARTBEAT, receiver_id, session_id)
        
        payload = {
            'timestamp': int(time.time()),
            'status': 'alive'
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def create_error_message(self, receiver_id: str, error_code: str, 
                           error_message: str, session_id: Optional[str] = None) -> ProtocolMessage:
        """创建错误消息"""
        header = self.create_message_header(MessageType.ERROR, receiver_id, session_id)
        
        payload = {
            'error_code': error_code,
            'error_message': error_message,
            'timestamp': int(time.time())
        }
        
        return ProtocolMessage(header=header, payload=payload)
    
    def sign_message(self, message: ProtocolMessage, private_key) -> None:
        """为消息添加数字签名"""
        # 序列化消息内容
        message_data = self.serialize_message(message)
        
        # 这里需要根据实际的私钥类型选择签名方法
        # 简化处理，使用HMAC作为签名
        if hasattr(private_key, 'sign'):
            # RSA/ECC签名
            signature = private_key.sign(message_data.encode())
            message.signature = base64.b64encode(signature).decode('ascii')
        else:
            # HMAC签名
            mac = self.auth.hmac_sha256(message_data, private_key)
            message.mac = base64.b64encode(mac).decode('ascii')
    
    def verify_message(self, message: ProtocolMessage, public_key) -> bool:
        """验证消息签名"""
        try:
            # 临时移除签名字段
            original_signature = message.signature
            original_mac = message.mac
            message.signature = None
            message.mac = None
            
            message_data = self.serialize_message(message)
            
            # 恢复签名字段
            message.signature = original_signature
            message.mac = original_mac
            
            if original_signature and hasattr(public_key, 'verify'):
                # RSA/ECC验证
                signature = base64.b64decode(original_signature)
                public_key.verify(signature, message_data.encode())
                return True
            elif original_mac:
                # HMAC验证
                mac = base64.b64decode(original_mac)
                return self.auth.verify_hmac(message_data, mac, public_key)
            
            return False
        except Exception:
            return False
    
    def serialize_message(self, message: ProtocolMessage) -> str:
        """序列化消息"""
        # 转换为字典
        message_dict = {
            'header': asdict(message.header),
            'payload': message.payload,
            'signature': message.signature,
            'mac': message.mac
        }
        
        # 处理枚举类型
        message_dict['header']['message_type'] = message.header.message_type.value
        
        return json.dumps(message_dict, sort_keys=True)
    
    def deserialize_message(self, message_data: str) -> ProtocolMessage:
        """反序列化消息"""
        message_dict = json.loads(message_data)
        
        # 重建头部
        header_dict = message_dict['header']
        header_dict['message_type'] = MessageType(header_dict['message_type'])
        header = MessageHeader(**header_dict)
        
        return ProtocolMessage(
            header=header,
            payload=message_dict['payload'],
            signature=message_dict.get('signature'),
            mac=message_dict.get('mac')
        )
    
    def decrypt_text_message(self, message: ProtocolMessage) -> str:
        """解密文本消息"""
        if message.header.session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        session_key = self.sessions[message.header.session_id]['session_key']
        encrypted_content = message.payload['encrypted_content']
        
        return self.crypto.decrypt_message(
            encrypted_content, session_key, 
            message.header.sender_id, message.header.receiver_id
        )
    
    def decrypt_file_chunk(self, message: ProtocolMessage) -> bytes:
        """解密文件块"""
        if message.header.session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        session_key = self.sessions[message.header.session_id]['session_key']
        encrypted_chunk = base64.b64decode(message.payload['encrypted_chunk'])
        nonce = base64.b64decode(message.payload['nonce'])
        
        return self.crypto.decrypt(encrypted_chunk, session_key, nonce)
    
    def generate_session_id(self, peer_id: str) -> str:
        """生成会话ID"""
        timestamp = int(time.time())
        data = f"{self.user_id}:{peer_id}:{timestamp}:{secrets.token_hex(16)}"
        hash_bytes = SecureHash.sha256(data)
        return base64.b64encode(hash_bytes).decode('ascii')[:16]
    
    def cleanup_expired_sessions(self, max_age: int = 3600) -> int:
        """清理过期会话"""
        current_time = int(time.time())
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            if current_time - session['last_activity'] > max_age:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
        
        return len(expired_sessions)


# 示例使用
if __name__ == "__main__":
    print("=== 安全通信协议测试 ===")
    
    # 创建两个协议实例（模拟Alice和Bob）
    alice_protocol = SecureProtocol("Alice")
    bob_protocol = SecureProtocol("Bob")
    
    # 模拟握手过程
    print("--- 握手过程 ---")
    
    # Alice发起握手
    alice_public_key = b"alice_public_key_data"
    handshake_init = alice_protocol.create_handshake_init("Bob", alice_public_key)
    print(f"✓ Alice发起握手: {handshake_init.header.message_id}")
    
    # Bob响应握手
    bob_public_key = b"bob_public_key_data"
    session_key = secrets.token_bytes(32)  # 模拟会话密钥
    handshake_response = bob_protocol.create_handshake_response(
        handshake_init, bob_public_key, session_key
    )
    print(f"✓ Bob响应握手: {handshake_response.header.message_id}")
    
    # Alice完成握手
    session_id = handshake_response.header.session_id
    alice_protocol.sessions[session_id] = {
        'peer_id': 'Bob',
        'session_key': session_key,
        'created_at': int(time.time()),
        'last_activity': int(time.time())
    }
    
    handshake_complete = alice_protocol.create_handshake_complete(handshake_response)
    print(f"✓ Alice完成握手: {handshake_complete.header.message_id}")
    
    # 发送文本消息
    print("\n--- 文本消息 ---")
    text_message = alice_protocol.create_text_message("Bob", "Hello, Bob!", session_id)
    print(f"✓ Alice发送消息: {text_message.header.message_id}")
    
    # Bob解密消息
    bob_protocol.sessions[session_id] = alice_protocol.sessions[session_id]
    decrypted_text = bob_protocol.decrypt_text_message(text_message)
    print(f"✓ Bob收到消息: {decrypted_text}")
    
    # 文件传输
    print("\n--- 文件传输 ---")
    file_init = alice_protocol.create_file_transfer_init(
        "Bob", "test.txt", 1024, "file_hash_123", session_id
    )
    print(f"✓ Alice初始化文件传输: {file_init.payload['filename']}")
    
    # 发送文件块
    test_chunk = b"This is test file content"
    file_chunk = alice_protocol.create_file_chunk(
        "Bob", file_init.payload['transfer_id'], test_chunk, 0, 1, session_id
    )
    print(f"✓ Alice发送文件块: {file_chunk.header.chunk_index}")
    
    # Bob解密文件块
    decrypted_chunk = bob_protocol.decrypt_file_chunk(file_chunk)
    print(f"✓ Bob收到文件块: {decrypted_chunk}")
    
    # 消息序列化测试
    print("\n--- 消息序列化 ---")
    serialized = alice_protocol.serialize_message(text_message)
    deserialized = alice_protocol.deserialize_message(serialized)
    print(f"✓ 消息序列化测试: {deserialized.header.message_id == text_message.header.message_id}")
    
    print("\n=== 协议测试完成 ===")
