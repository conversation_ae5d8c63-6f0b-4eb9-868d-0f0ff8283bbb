"""
匿名加密通讯系统 - 主程序入口
支持客户端和中继服务器模式
"""

import argparse
import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.gui import MainWindow, run_application
from src.network import RelayServer, start_relay_network
from src.crypto import run_crypto_tests


def setup_logging(level=logging.INFO):
    """设置日志"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('anonymous_chat.log', encoding='utf-8')
        ]
    )


def run_client():
    """运行客户端"""
    print("启动匿名加密通讯客户端...")
    try:
        run_application()
    except Exception as e:
        print(f"客户端运行错误: {e}")
        return 1
    return 0


async def run_relay_server(host='localhost', port=8001, server_id=None):
    """运行单个中继服务器"""
    print(f"启动中继服务器 {server_id or 'default'} on {host}:{port}")
    
    try:
        server = RelayServer(host, port, server_id)
        await server.start()
    except KeyboardInterrupt:
        print("\n中继服务器已停止")
    except Exception as e:
        print(f"中继服务器运行错误: {e}")
        return 1
    return 0


async def run_relay_network_cmd(ports=None):
    """运行中继网络"""
    if ports is None:
        ports = [8001, 8002, 8003]
    
    print(f"启动中继网络，端口: {ports}")
    
    try:
        await start_relay_network(ports)
    except KeyboardInterrupt:
        print("\n中继网络已停止")
    except Exception as e:
        print(f"中继网络运行错误: {e}")
        return 1
    return 0


def run_tests():
    """运行测试"""
    print("运行加密模块测试...")
    
    try:
        # 运行加密测试
        run_crypto_tests()
        
        # 运行网络测试
        print("\n运行网络模块测试...")
        from src.network import test_anonymous_communication
        asyncio.run(test_anonymous_communication())
        
        print("\n✓ 所有测试完成")
        return 0
        
    except Exception as e:
        print(f"测试运行错误: {e}")
        return 1


def create_config_files():
    """创建配置文件"""
    print("创建配置文件...")
    
    try:
        # 创建配置目录
        os.makedirs("config", exist_ok=True)
        
        # 客户端配置
        client_config = {
            "user_id": "",
            "relay_servers": [
                "ws://localhost:8001",
                "ws://localhost:8002", 
                "ws://localhost:8003"
            ],
            "auto_connect": False,
            "identity_rotation_interval": 3600,
            "circuit_length": 3,
            "use_dummy_traffic": True,
            "contacts": {}
        }
        
        import json
        with open("config/client_config.json", 'w', encoding='utf-8') as f:
            json.dump(client_config, f, ensure_ascii=False, indent=2)
        
        # 服务器配置
        server_config = {
            "host": "localhost",
            "port": 8001,
            "max_connections": 1000,
            "connection_timeout": 300,
            "log_level": "INFO"
        }
        
        with open("config/server_config.json", 'w', encoding='utf-8') as f:
            json.dump(server_config, f, ensure_ascii=False, indent=2)
        
        print("✓ 配置文件已创建")
        return 0
        
    except Exception as e:
        print(f"创建配置文件错误: {e}")
        return 1


def show_system_info():
    """显示系统信息"""
    print("=== 匿名加密通讯系统信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"项目路径: {project_root}")
    
    # 检查依赖
    try:
        import cryptography
        print(f"cryptography版本: {cryptography.__version__}")
    except ImportError:
        print("cryptography: 未安装")
    
    try:
        import websockets
        print(f"websockets版本: {websockets.__version__}")
    except ImportError:
        print("websockets: 未安装")
    
    try:
        import tkinter
        print(f"tkinter: 可用")
    except ImportError:
        print("tkinter: 不可用")
    
    # 显示配置文件状态
    config_files = [
        "config/client_config.json",
        "config/server_config.json"
    ]
    
    print("\n配置文件状态:")
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"  ✓ {config_file}")
        else:
            print(f"  ✗ {config_file}")
    
    print("\n可用命令:")
    print("  python src/main.py client          - 启动客户端")
    print("  python src/main.py relay           - 启动单个中继服务器")
    print("  python src/main.py network         - 启动中继网络")
    print("  python src/main.py test            - 运行测试")
    print("  python src/main.py config          - 创建配置文件")
    print("  python src/main.py info            - 显示系统信息")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="匿名加密通讯系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python src/main.py client                    # 启动客户端
  python src/main.py relay                     # 启动中继服务器
  python src/main.py relay --port 8002         # 在指定端口启动中继服务器
  python src/main.py network                   # 启动中继网络
  python src/main.py network --ports 8001 8002 8003  # 指定端口启动网络
  python src/main.py test                      # 运行测试
  python src/main.py config                    # 创建配置文件
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['client', 'relay', 'network', 'test', 'config', 'info'],
        help='运行模式'
    )
    
    parser.add_argument(
        '--host',
        default='localhost',
        help='服务器主机地址 (默认: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8001,
        help='服务器端口 (默认: 8001)'
    )
    
    parser.add_argument(
        '--ports',
        type=int,
        nargs='+',
        default=[8001, 8002, 8003],
        help='网络模式的端口列表 (默认: 8001 8002 8003)'
    )
    
    parser.add_argument(
        '--server-id',
        help='服务器ID'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='匿名加密通讯系统 v1.0.0'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level)
    
    # 根据模式执行相应操作
    try:
        if args.mode == 'client':
            return run_client()
        
        elif args.mode == 'relay':
            return asyncio.run(run_relay_server(args.host, args.port, args.server_id))
        
        elif args.mode == 'network':
            return asyncio.run(run_relay_network_cmd(args.ports))
        
        elif args.mode == 'test':
            return run_tests()
        
        elif args.mode == 'config':
            return create_config_files()
        
        elif args.mode == 'info':
            show_system_info()
            return 0
        
        else:
            parser.print_help()
            return 1
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行错误: {e}")
        logging.exception("程序异常")
        return 1


if __name__ == "__main__":
    sys.exit(main())
