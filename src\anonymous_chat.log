2025-06-17 20:11:50,981 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:11:50,981 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:567> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:11:50,982 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-17 20:12:07,119 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,120 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:567> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,121 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,123 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() running at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:570> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,143 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-17 20:12:07,144 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-13' coro=<MainWindow._message_receiver() running at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:570> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:28,270 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:28,271 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:725> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:30,711 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 09:19:30,712 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:725> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:32,709 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:32,710 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() done, defined at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:725> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:35,852 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 10:36:35,852 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<MainWindow._message_receiver() running at D:\警大\学科课程\数据安全技术\obe\src\gui\main_window.py:728> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 19:16:33,871 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:84> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-18 20:13:08,679 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:87> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,679 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,680 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,683 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,686 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future cancelled>>
2025-06-18 20:13:08,686 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,688 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future cancelled>>
2025-06-18 20:13:08,689 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:621> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,689 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:635> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,689 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:651> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,689 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:668> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:13:08,693 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-13' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:116> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-18 20:17:07,821 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-15' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:87> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,823 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-18' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,824 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-20' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,824 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-19' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,824 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-16' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,831 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-21' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,836 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-17' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,840 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-22' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:621> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,840 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-23' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:635> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,840 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-24' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:651> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,841 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-25' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:668> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:07,843 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-28' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:116> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-18 20:17:10,992 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,994 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:222> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,994 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,996 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:222> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,996 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:621> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,997 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:635> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,998 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:651> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,998 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:668> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:10,998 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-15' coro=<AnonymousClient.stop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:116> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-18 20:17:10,999 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-18 20:17:11,001 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future cancelled>>
2025-06-18 20:17:11,001 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:87> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:17:11,002 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:222> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:621> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:635> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:651> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:668> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-13' coro=<AnonymousClient.stop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:116> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-18 20:22:21,009 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future cancelled>>
2025-06-18 20:22:21,017 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:225> wait_for=<Future cancelled>>
2025-06-18 20:22:21,017 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:87> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,019 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-18 20:22:21,019 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:27:31,734 - root - ERROR - 程序异常
Traceback (most recent call last):
  File "D:\警大\学科课程\数据安全技术\obe\src\main.py", line 275, in main
    return run_client()
           ^^^^^^^^^^^^
  File "D:\警大\学科课程\数据安全技术\obe\src\main.py", line 50, in run_client
    asyncio.create_task(client.start())
  File "D:\anaconda\Lib\asyncio\tasks.py", line 371, in create_task
    loop = events.get_running_loop()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-06-19 00:47:34,028 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,028 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,038 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,039 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,039 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,039 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,039 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:733> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,041 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:747> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,042 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:763> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,042 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:780> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:34,042 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:125> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 00:47:34,046 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-19 00:47:34,047 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-19 00:47:34,048 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:96> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,927 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,928 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,929 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,929 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,930 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,930 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,930 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:733> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,930 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:747> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,931 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:763> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,931 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:780> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:47:35,932 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-13' coro=<AnonymousClient.stop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:125> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 00:47:35,932 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-19 00:47:35,933 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-19 00:47:35,934 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:96> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,899 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,901 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,902 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,903 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,904 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,905 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:231> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,905 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:746> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,906 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._identity_rotation_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:760> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,907 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-11' coro=<AnonymousClient._circuit_maintenance_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:776> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,908 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:793> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 00:51:16,914 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:125> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 00:51:16,915 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-19 00:51:16,915 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending cb=[_ProactorReadPipeTransport._loop_reading()]>
Traceback (most recent call last):
  File "D:\anaconda\Lib\asyncio\windows_events.py", line 71, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
2025-06-19 00:51:16,915 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:96> wait_for=<Future pending cb=[Task.task_wakeup()]>>
