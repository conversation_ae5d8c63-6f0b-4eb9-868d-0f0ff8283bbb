2025-06-17 16:30:38,308 - RelayServer-relay_d7d13e06 - INFO - 启动中继服务器 relay_d7d13e06 on localhost:8001
2025-06-17 16:30:38,320 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 16:30:38,320 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 16:30:38,320 - RelayServer-relay_d7d13e06 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 16:35:48,194 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 16:35:48,194 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 16:35:48,194 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 16:35:48,204 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 16:35:48,204 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 16:35:48,204 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 16:35:48,204 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 16:35:48,204 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 16:35:48,204 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 16:35:48,204 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 16:35:48,204 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 16:35:48,204 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 16:40:09,037 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 16:40:09,037 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 16:40:09,037 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 16:40:09,045 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 16:40:09,045 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 16:40:09,045 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 16:40:09,045 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 16:40:09,045 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 16:40:09,045 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 16:40:09,045 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 16:40:09,045 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 16:40:09,045 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 16:41:21,126 - websockets.server - INFO - connection open
2025-06-17 16:41:21,126 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-17 16:41:21,131 - websockets.server - INFO - connection open
2025-06-17 16:41:21,131 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-17 16:41:21,135 - websockets.server - INFO - connection open
2025-06-17 16:41:21,136 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-17 16:42:20,599 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 16:42:20,599 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 16:42:20,599 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 16:42:20,613 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 16:42:20,613 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 16:42:20,613 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 16:42:20,613 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 16:42:20,613 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 16:42:20,613 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 16:42:20,613 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 16:42:20,613 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 16:42:20,613 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 16:43:53,720 - websockets.server - INFO - connection open
2025-06-17 16:43:53,720 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_6182c7658277570a from ('127.0.0.1', 53734)
2025-06-17 16:43:53,721 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:43:53,723 - websockets.server - INFO - connection open
2025-06-17 16:43:53,723 - RelayServer-relay_002 - INFO - ✓ 客户端连接: client_74c46922d4931c6b from ('127.0.0.1', 53735)
2025-06-17 16:43:53,723 - RelayServer-relay_002 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:43:53,726 - websockets.server - INFO - connection open
2025-06-17 16:43:53,726 - RelayServer-relay_003 - INFO - ✓ 客户端连接: client_5b8de47a494cc5e2 from ('127.0.0.1', 53736)
2025-06-17 16:43:53,728 - RelayServer-relay_003 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:47:20,620 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 1, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:47:20,620 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 1, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:47:20,620 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 1, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:48:22,140 - websockets.server - INFO - connection open
2025-06-17 16:48:22,140 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_2c93739e1c565105 from ('127.0.0.1', 53896)
2025-06-17 16:48:49,703 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:49:20,643 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:49:20,643 - RelayServer-relay_001 - INFO - 清理了 1 个过期连接
2025-06-17 16:49:20,643 - RelayServer-relay_003 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:49:20,643 - RelayServer-relay_003 - INFO - 清理了 1 个过期连接
2025-06-17 16:49:20,643 - RelayServer-relay_002 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:49:20,643 - RelayServer-relay_002 - INFO - 清理了 1 个过期连接
2025-06-17 16:49:24,708 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:49:45,718 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:49:59,215 - websockets.server - INFO - connection open
2025-06-17 16:49:59,215 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_8e5ccbe63cee6abb from ('127.0.0.1', 53946)
2025-06-17 16:50:20,651 - RelayServer-relay_002 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:50:20,651 - RelayServer-relay_002 - INFO - 清理了 1 个过期连接
2025-06-17 16:50:20,651 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:50:20,651 - RelayServer-relay_001 - INFO - 清理了 1 个过期连接
2025-06-17 16:50:20,651 - RelayServer-relay_003 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:50:20,651 - RelayServer-relay_003 - INFO - 清理了 1 个过期连接
2025-06-17 16:51:17,127 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:51:18,738 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:51:19,729 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:51:20,656 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:51:20,656 - RelayServer-relay_001 - INFO - 清理了 1 个过期连接
2025-06-17 16:51:20,656 - RelayServer-relay_003 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:51:20,656 - RelayServer-relay_003 - INFO - 清理了 1 个过期连接
2025-06-17 16:51:20,656 - RelayServer-relay_002 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:51:20,656 - RelayServer-relay_002 - INFO - 清理了 1 个过期连接
2025-06-17 16:51:46,747 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:52:20,629 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 1, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:52:20,629 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 1, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:52:20,629 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 3, 总连接: 3, 中继消息: 4, 传输字节: 210
2025-06-17 16:52:20,662 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:52:20,663 - RelayServer-relay_001 - INFO - 清理了 1 个过期连接
2025-06-17 16:52:20,663 - RelayServer-relay_002 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:52:20,663 - RelayServer-relay_002 - INFO - 清理了 1 个过期连接
2025-06-17 16:52:20,663 - RelayServer-relay_003 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-17 16:52:20,663 - RelayServer-relay_003 - INFO - 清理了 1 个过期连接
2025-06-17 16:52:25,741 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:52:52,962 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 16:52:52,966 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 16:52:52,966 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 16:52:52,979 - RelayServer-relay_001 - INFO - 正在停止中继服务器...
2025-06-17 16:52:52,979 - RelayServer-relay_001 - INFO - ✓ 中继服务器已停止
2025-06-17 16:52:52,979 - RelayServer-relay_002 - INFO - 正在停止中继服务器...
2025-06-17 16:52:52,979 - RelayServer-relay_002 - INFO - ✓ 中继服务器已停止
2025-06-17 16:52:52,979 - RelayServer-relay_003 - INFO - 正在停止中继服务器...
2025-06-17 16:52:52,979 - RelayServer-relay_003 - INFO - ✓ 中继服务器已停止
2025-06-17 16:52:54,750 - RelayServer-relay_001 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-17 16:53:32,777 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 16:53:32,777 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 16:53:32,777 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 16:53:32,787 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 16:53:32,787 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 16:53:32,787 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 16:53:32,787 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 16:53:32,787 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 16:53:32,787 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 16:53:32,787 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 16:53:32,787 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 16:53:32,787 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 16:55:08,787 - websockets.server - INFO - connection open
2025-06-17 16:55:08,787 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_41341d35d83de0c3 from ('127.0.0.1', 54139)
2025-06-17 16:55:12,565 - RelayServer-relay_001 - INFO - 客户端已断开: client_41341d35d83de0c3 - Unknown
2025-06-17 16:55:29,607 - websockets.server - INFO - connection open
2025-06-17 16:55:29,607 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_18eea0b9ef63461a from ('127.0.0.1', 54152)
2025-06-17 16:55:29,608 - RelayServer-relay_001 - INFO - 客户端已断开: client_18eea0b9ef63461a - Unknown
2025-06-17 16:55:43,331 - websockets.server - INFO - connection open
2025-06-17 16:55:43,332 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_ecb1c8e0dabb3d3f from ('127.0.0.1', 54166)
2025-06-17 16:55:43,333 - RelayServer-relay_001 - INFO - 客户端已断开: client_ecb1c8e0dabb3d3f - Unknown
2025-06-17 16:55:52,160 - websockets.server - INFO - connection open
2025-06-17 16:55:52,160 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_1144c353d46a8464 from ('127.0.0.1', 54172)
2025-06-17 16:55:52,161 - RelayServer-relay_001 - INFO - 客户端已断开: client_1144c353d46a8464 - Unknown
2025-06-17 16:55:52,163 - websockets.server - INFO - connection open
2025-06-17 16:55:52,163 - RelayServer-relay_002 - INFO - ✓ 客户端连接: client_4c52f893fc9526e1 from ('127.0.0.1', 54173)
2025-06-17 16:55:52,165 - RelayServer-relay_002 - INFO - 客户端已断开: client_4c52f893fc9526e1 - Unknown
2025-06-17 16:55:52,167 - websockets.server - INFO - connection open
2025-06-17 16:55:52,167 - RelayServer-relay_003 - INFO - ✓ 客户端连接: client_2831049c6f6e750a from ('127.0.0.1', 54174)
2025-06-17 16:55:52,168 - RelayServer-relay_003 - INFO - 客户端已断开: client_2831049c6f6e750a - Unknown
2025-06-17 16:58:32,806 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:58:32,806 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 16:58:32,806 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:03:32,818 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:03:32,818 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:03:32,818 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:08:32,829 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:08:32,829 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:08:32,829 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:13:32,843 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:13:32,843 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:13:32,843 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:18:32,890 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:18:32,890 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:18:32,890 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:23:32,932 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1800s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:23:32,932 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1800s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:23:32,932 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1800s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:28:32,976 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 2100s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:28:32,976 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 2100s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:28:32,976 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 2100s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:33:33,010 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 2401s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:33:33,010 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 2401s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:33:33,010 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 2401s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:38:33,063 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 2701s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:38:33,063 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 2701s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:38:33,063 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 2701s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:43:33,102 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 3001s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:43:33,102 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 3001s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:43:33,102 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 3001s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:48:33,129 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 3301s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:48:33,129 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 3301s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:48:33,129 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 3301s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:53:33,162 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 3601s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:53:33,162 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 3601s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 17:53:33,162 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 3601s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:58:33,186 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 3901s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:58:33,186 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 3901s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 17:58:33,186 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 3901s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:03:33,201 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 4201s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:03:33,201 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 4201s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:03:33,201 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 4201s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:08:33,229 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 4501s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:08:33,229 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 4501s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:08:33,229 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 4501s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:13:33,243 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 4801s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:13:33,243 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 4801s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:13:33,243 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 4801s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:18:33,256 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 5101s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:18:33,256 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 5101s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:18:33,256 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 5101s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:23:33,294 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 5401s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:23:33,294 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 5401s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:23:33,294 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 5401s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:28:33,319 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 5701s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:28:33,319 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 5701s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:28:33,319 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 5701s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:33:33,349 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 6001s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:33:33,349 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 6001s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:33:33,349 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 6001s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:38:33,359 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 6301s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:38:33,359 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 6301s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:38:33,359 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 6301s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:43:33,371 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 6601s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:43:33,371 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 6601s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:43:33,371 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 6601s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:48:33,384 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 6901s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:48:33,384 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 6901s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:48:33,384 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 6901s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:53:33,392 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 7201s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:53:33,392 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 7201s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:53:33,392 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 7201s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:58:33,392 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 7501s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 18:58:33,392 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 7501s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 18:58:33,392 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 7501s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:03:33,402 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 7801s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:03:33,402 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 7801s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:03:33,402 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 7801s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 19:08:33,418 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 8101s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 19:08:33,418 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 8101s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:08:33,418 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 8101s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:13:33,440 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 8401s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:13:33,440 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 8401s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:13:33,440 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 8401s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 158
2025-06-17 19:17:19,449 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 19:17:19,458 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 19:17:19,458 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 19:17:19,474 - RelayServer-relay_001 - INFO - 正在停止中继服务器...
2025-06-17 19:17:19,474 - RelayServer-relay_001 - INFO - ✓ 中继服务器已停止
2025-06-17 19:17:19,475 - RelayServer-relay_002 - INFO - 正在停止中继服务器...
2025-06-17 19:17:19,475 - RelayServer-relay_002 - INFO - ✓ 中继服务器已停止
2025-06-17 19:17:19,475 - RelayServer-relay_003 - INFO - 正在停止中继服务器...
2025-06-17 19:17:19,475 - RelayServer-relay_003 - INFO - ✓ 中继服务器已停止
2025-06-17 19:17:46,639 - websockets.server - INFO - connection open
2025-06-17 19:17:46,639 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_ecc0f8622e1230a2 from ('127.0.0.1', 56865)
2025-06-17 19:17:46,639 - RelayServer-relay_001 - INFO - 客户端已断开: client_ecc0f8622e1230a2 - Unknown
2025-06-17 19:17:53,053 - websockets.server - INFO - connection open
2025-06-17 19:17:53,058 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_dbe0ac60a1123685 from ('127.0.0.1', 56869)
2025-06-17 19:17:53,059 - RelayServer-relay_001 - INFO - 客户端已断开: client_dbe0ac60a1123685 - Unknown
2025-06-17 19:17:58,445 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:77> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-17 19:18:01,696 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:77> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-17 19:18:33,438 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 8701s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:18:33,438 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 8701s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:18:33,438 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 8701s, 活跃连接: 0, 总连接: 6, 中继消息: 6, 传输字节: 240
2025-06-17 19:23:33,448 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 9001s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:23:33,448 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 9001s, 活跃连接: 0, 总连接: 6, 中继消息: 6, 传输字节: 240
2025-06-17 19:23:33,448 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 9001s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:26:21,355 - websockets.server - INFO - connection open
2025-06-17 19:26:21,355 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_7502ca40d49d47e3 from ('127.0.0.1', 57121)
2025-06-17 19:26:21,356 - RelayServer-relay_001 - INFO - 客户端已断开: client_7502ca40d49d47e3 - Unknown
2025-06-17 19:26:42,968 - websockets.server - INFO - connection open
2025-06-17 19:26:42,968 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_c9059ad2e2beddd4 from ('127.0.0.1', 57138)
2025-06-17 19:26:42,969 - RelayServer-relay_001 - INFO - 客户端已断开: client_c9059ad2e2beddd4 - Unknown
2025-06-17 19:28:33,446 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 9301s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:28:33,446 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 9301s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:28:33,446 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 9301s, 活跃连接: 0, 总连接: 8, 中继消息: 8, 传输字节: 322
2025-06-17 19:28:59,917 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:77> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-17 19:30:56,275 - websockets.server - INFO - connection open
2025-06-17 19:30:56,275 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_74407beee53854c5 from ('127.0.0.1', 57264)
2025-06-17 19:30:56,276 - RelayServer-relay_001 - INFO - 客户端已断开: client_74407beee53854c5 - Unknown
2025-06-17 19:31:22,906 - websockets.server - INFO - connection open
2025-06-17 19:31:22,906 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_40a5822852239deb from ('127.0.0.1', 57300)
2025-06-17 19:31:22,907 - RelayServer-relay_001 - INFO - 客户端已断开: client_40a5822852239deb - Unknown
2025-06-17 19:31:49,111 - websockets.server - INFO - connection open
2025-06-17 19:31:49,111 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_a01b0be3a3f5576b from ('127.0.0.1', 57309)
2025-06-17 19:31:49,111 - RelayServer-relay_001 - INFO - 客户端已断开: client_a01b0be3a3f5576b - Unknown
2025-06-17 19:32:23,608 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:77> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-17 19:33:33,462 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 9601s, 活跃连接: 0, 总连接: 11, 中继消息: 11, 传输字节: 445
2025-06-17 19:33:33,462 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 9601s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:33:33,462 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 9601s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:37:22,154 - websockets.server - INFO - connection open
2025-06-17 19:37:22,154 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_e0c434e086f05095 from ('127.0.0.1', 57507)
2025-06-17 19:37:22,156 - RelayServer-relay_001 - INFO - 客户端已断开: client_e0c434e086f05095 - Unknown
2025-06-17 19:37:46,505 - websockets.server - INFO - connection open
2025-06-17 19:37:46,506 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_da55eb5cc00c66d9 from ('127.0.0.1', 57528)
2025-06-17 19:37:46,506 - RelayServer-relay_001 - INFO - 客户端已断开: client_da55eb5cc00c66d9 - Unknown
2025-06-17 19:38:33,465 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 9901s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:38:33,465 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 9901s, 活跃连接: 0, 总连接: 13, 中继消息: 13, 传输字节: 527
2025-06-17 19:38:33,465 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 9901s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:43:33,468 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 10201s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:43:33,468 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 10201s, 活跃连接: 0, 总连接: 13, 中继消息: 13, 传输字节: 527
2025-06-17 19:43:33,468 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 10201s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:48:33,480 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 10501s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:48:33,480 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 10501s, 活跃连接: 0, 总连接: 13, 中继消息: 13, 传输字节: 527
2025-06-17 19:48:33,480 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 10501s, 活跃连接: 0, 总连接: 1, 中继消息: 1, 传输字节: 35
2025-06-17 19:55:10,746 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 19:55:10,750 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 19:55:10,750 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 19:55:10,760 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 19:55:10,760 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 19:55:10,760 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 19:55:10,760 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 19:55:10,760 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 19:55:10,760 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 19:55:10,761 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 19:55:10,762 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 19:55:10,762 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 19:56:37,898 - websockets.server - INFO - connection open
2025-06-17 19:56:37,898 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_32cf815d06de41e8 from ('127.0.0.1', 58273)
2025-06-17 19:56:37,899 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_32cf815d06de41e8
2025-06-17 19:57:12,333 - websockets.server - INFO - connection open
2025-06-17 19:57:12,334 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_8d13f93e1f2f14bd from ('127.0.0.1', 58297)
2025-06-17 19:57:12,334 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_8d13f93e1f2f14bd
2025-06-17 19:59:58,775 - websockets.server - INFO - connection open
2025-06-17 19:59:58,775 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_55e143e4cc1f0556 from ('127.0.0.1', 58400)
2025-06-17 19:59:58,775 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: test_alice -> client_55e143e4cc1f0556
2025-06-17 19:59:58,775 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: test_alice -> aaa123
2025-06-17 19:59:58,784 - RelayServer-relay_001 - INFO - 客户端已断开: client_55e143e4cc1f0556 - Unknown
2025-06-17 20:00:10,767 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:00:10,767 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:00:10,767 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 3, 中继消息: 5, 传输字节: 437
2025-06-17 20:02:10,793 - RelayServer-relay_001 - INFO - 客户端已断开: client_32cf815d06de41e8 - Unknown
2025-06-17 20:02:10,793 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'client_32cf815d06de41e8'
2025-06-17 20:02:10,793 - RelayServer-relay_001 - INFO - 清理了 1 个过期连接
2025-06-17 20:03:10,800 - RelayServer-relay_001 - INFO - 客户端已断开: client_8d13f93e1f2f14bd - Unknown
2025-06-17 20:03:10,800 - RelayServer-relay_001 - ERROR - 断开客户端连接时出错: 'client_8d13f93e1f2f14bd'
2025-06-17 20:03:10,800 - RelayServer-relay_001 - INFO - 清理了 1 个过期连接
2025-06-17 20:05:10,769 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:05:10,769 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:05:10,769 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 3, 中继消息: 5, 传输字节: 437
2025-06-17 20:06:15,330 - websockets.server - INFO - connection open
2025-06-17 20:06:15,330 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_af71f8564701ad87 from ('127.0.0.1', 58639)
2025-06-17 20:06:15,330 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_af71f8564701ad87
2025-06-17 20:06:39,471 - websockets.server - INFO - connection open
2025-06-17 20:06:39,472 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_6fe0971b72567356 from ('127.0.0.1', 58662)
2025-06-17 20:06:39,472 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_6fe0971b72567356
2025-06-17 20:07:03,876 - websockets.server - INFO - connection open
2025-06-17 20:07:03,876 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_3ab927d2e9f6e4b9 from ('127.0.0.1', 58681)
2025-06-17 20:07:03,877 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_3ab927d2e9f6e4b9
2025-06-17 20:07:29,499 - RelayServer-relay_001 - INFO - 客户端断开连接: client_6fe0971b72567356
2025-06-17 20:07:29,499 - RelayServer-relay_001 - INFO - 客户端已断开: client_6fe0971b72567356 - Unknown
2025-06-17 20:07:29,499 - RelayServer-relay_001 - ERROR - 发送消息到客户端 client_6fe0971b72567356 时出错: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-06-17 20:07:29,499 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: bbb123 -> aaa123
2025-06-17 20:08:07,339 - websockets.server - INFO - connection open
2025-06-17 20:08:07,339 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_11cecf652d9b2894 from ('127.0.0.1', 58728)
2025-06-17 20:08:07,343 - websockets.server - INFO - connection open
2025-06-17 20:08:07,343 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_0397a20f9ae1639f from ('127.0.0.1', 58729)
2025-06-17 20:08:07,343 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: alice_test -> client_11cecf652d9b2894
2025-06-17 20:08:07,343 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bob_test -> client_0397a20f9ae1639f
2025-06-17 20:08:07,347 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: alice_test -> bob_test
2025-06-17 20:08:07,347 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: bob_test -> alice_test
2025-06-17 20:08:07,348 - RelayServer-relay_001 - INFO - 客户端已断开: client_11cecf652d9b2894 - Unknown
2025-06-17 20:08:07,349 - RelayServer-relay_001 - INFO - 客户端已断开: client_0397a20f9ae1639f - Unknown
2025-06-17 20:10:10,781 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:10:10,781 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:10:10,781 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 8, 中继消息: 14, 传输字节: 1331
2025-06-17 20:10:43,910 - websockets.server - INFO - connection open
2025-06-17 20:10:43,910 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_dacf612158b048a6 from ('127.0.0.1', 58812)
2025-06-17 20:10:43,912 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_dacf612158b048a6
2025-06-17 20:11:23,942 - RelayServer-relay_001 - INFO - 客户端断开连接: client_3ab927d2e9f6e4b9
2025-06-17 20:11:23,942 - RelayServer-relay_001 - INFO - 客户端已断开: client_3ab927d2e9f6e4b9 - Unknown
2025-06-17 20:11:50,981 - RelayServer-relay_001 - INFO - 客户端断开连接: client_af71f8564701ad87
2025-06-17 20:11:50,981 - RelayServer-relay_001 - INFO - 客户端已断开: client_af71f8564701ad87 - Unknown
2025-06-17 20:12:07,144 - RelayServer-relay_001 - INFO - 客户端断开连接: client_dacf612158b048a6
2025-06-17 20:12:07,144 - RelayServer-relay_001 - INFO - 客户端已断开: client_dacf612158b048a6 - Unknown
2025-06-17 20:15:10,789 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:15:10,789 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 9, 中继消息: 19, 传输字节: 1868
2025-06-17 20:15:10,789 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:16:26,042 - websockets.server - INFO - connection open
2025-06-17 20:16:26,043 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_d7da5e8a648a99c0 from ('127.0.0.1', 59002)
2025-06-17 20:16:26,043 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_d7da5e8a648a99c0
2025-06-17 20:17:36,086 - websockets.server - INFO - connection open
2025-06-17 20:17:36,086 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_2fd04ce721b8612a from ('127.0.0.1', 59034)
2025-06-17 20:17:36,087 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_2fd04ce721b8612a
2025-06-17 20:17:57,736 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: bbb123 -> aaa123
2025-06-17 20:18:10,898 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: aaa123 -> bbb123
2025-06-17 20:18:25,681 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: aaa123 -> bbb123
2025-06-17 20:19:07,854 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: bbb123 -> aaa123
2025-06-17 20:20:10,796 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 2, 总连接: 11, 中继消息: 37, 传输字节: 3478
2025-06-17 20:20:10,796 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:20:10,796 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:23:19,914 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: aaa123 -> bbb123
2025-06-17 20:23:29,551 - RelayServer-relay_001 - INFO - ✓ 匿名消息已中继: bbb123 -> aaa123
2025-06-17 20:25:10,802 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1800s, 活跃连接: 2, 总连接: 11, 中继消息: 59, 传输字节: 5246
2025-06-17 20:25:10,802 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1800s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:25:10,802 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1800s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:30:10,816 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 2100s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:30:10,816 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 2100s, 活跃连接: 2, 总连接: 11, 中继消息: 79, 传输字节: 6766
2025-06-17 20:30:10,816 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 2100s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:31:57,321 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 20:31:57,326 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 20:31:57,326 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 20:31:57,338 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 20:31:57,338 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 20:31:57,338 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 20:31:57,338 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 20:31:57,338 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 20:31:57,338 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 20:31:57,338 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 20:31:57,338 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 20:31:57,338 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 20:34:49,125 - websockets.server - INFO - connection open
2025-06-17 20:34:49,126 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_4eb8f1b0c5db217c from ('127.0.0.1', 59738)
2025-06-17 20:34:49,126 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_4eb8f1b0c5db217c
2025-06-17 20:35:59,409 - websockets.server - INFO - connection open
2025-06-17 20:35:59,412 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_164ce457d1024686 from ('127.0.0.1', 59785)
2025-06-17 20:35:59,412 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_164ce457d1024686
2025-06-17 20:36:57,341 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:36:57,341 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:36:57,341 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 14, 传输字节: 1112
2025-06-17 20:38:57,152 - RelayServer-relay_001 - INFO - 客户端断开连接: client_4eb8f1b0c5db217c
2025-06-17 20:38:57,152 - RelayServer-relay_001 - INFO - 客户端已断开: client_4eb8f1b0c5db217c - Unknown
2025-06-17 20:39:06,717 - RelayServer-relay_001 - INFO - 客户端断开连接: client_164ce457d1024686
2025-06-17 20:39:06,717 - RelayServer-relay_001 - INFO - 客户端已断开: client_164ce457d1024686 - Unknown
2025-06-17 20:41:57,356 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:41:57,356 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:41:57,356 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 2, 中继消息: 31, 传输字节: 2428
2025-06-17 20:46:57,365 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 2, 中继消息: 31, 传输字节: 2428
2025-06-17 20:46:57,365 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:46:57,365 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:48:12,532 - websockets.server - INFO - connection open
2025-06-17 20:48:12,532 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_b6a442b25dfdfb71 from ('127.0.0.1', 60146)
2025-06-17 20:48:12,534 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_b6a442b25dfdfb71
2025-06-17 20:49:43,478 - RelayServer-relay_001 - INFO - 客户端断开连接: client_b6a442b25dfdfb71
2025-06-17 20:49:43,478 - RelayServer-relay_001 - INFO - 客户端已断开: client_b6a442b25dfdfb71 - Unknown
2025-06-17 20:51:14,223 - websockets.server - INFO - connection open
2025-06-17 20:51:14,223 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_773fb2a3a1fbc9d0 from ('127.0.0.1', 60234)
2025-06-17 20:51:14,225 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_773fb2a3a1fbc9d0
2025-06-17 20:51:57,372 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 1, 总连接: 4, 中继消息: 33, 传输字节: 2606
2025-06-17 20:51:57,372 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:51:57,372 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:52:18,994 - RelayServer-relay_001 - INFO - 客户端断开连接: client_773fb2a3a1fbc9d0
2025-06-17 20:52:18,997 - RelayServer-relay_001 - INFO - 客户端已断开: client_773fb2a3a1fbc9d0 - Unknown
2025-06-17 20:52:53,143 - websockets.server - INFO - connection open
2025-06-17 20:52:53,144 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_0ba019406395e04b from ('127.0.0.1', 60301)
2025-06-17 20:52:53,144 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_0ba019406395e04b
2025-06-17 20:53:25,709 - websockets.server - INFO - connection open
2025-06-17 20:53:25,709 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_c68cfffbcf94a1f9 from ('127.0.0.1', 60344)
2025-06-17 20:53:25,709 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_c68cfffbcf94a1f9
2025-06-17 20:53:44,679 - RelayServer-relay_001 - INFO - ✓ 密钥交换请求已转发: aaa123 -> bbb123
2025-06-17 20:53:44,680 - RelayServer-relay_001 - INFO - ✓ 密钥交换响应已转发: bbb123 -> aaa123
2025-06-17 20:53:44,681 - RelayServer-relay_001 - INFO - ✓ 加密消息已中继: aaa123 -> bbb123
2025-06-17 20:54:17,150 - websockets.server - INFO - connection open
2025-06-17 20:54:17,150 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_9b4003c9bc2ffa46 from ('127.0.0.1', 60396)
2025-06-17 20:54:17,150 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: test_sender -> client_9b4003c9bc2ffa46
2025-06-17 20:54:17,150 - RelayServer-relay_001 - INFO - ✓ 加密消息已中继: test_sender -> aaa123
2025-06-17 20:54:17,150 - RelayServer-relay_001 - INFO - 客户端已断开: client_9b4003c9bc2ffa46 - Unknown
2025-06-17 20:54:28,888 - RelayServer-relay_001 - INFO - ✓ 加密消息已中继: bbb123 -> aaa123
2025-06-17 20:54:38,163 - RelayServer-relay_001 - INFO - ✓ 加密消息已中继: aaa123 -> bbb123
2025-06-17 20:56:57,376 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:56:57,376 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 20:56:57,376 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 2, 总连接: 7, 中继消息: 56, 传输字节: 4913
2025-06-17 21:13:16,348 - RelayServer-relay_001 - INFO - 启动中继服务器 relay_001 on localhost:8001
2025-06-17 21:13:16,354 - RelayServer-relay_002 - INFO - 启动中继服务器 relay_002 on localhost:8002
2025-06-17 21:13:16,355 - RelayServer-relay_003 - INFO - 启动中继服务器 relay_003 on localhost:8003
2025-06-17 21:13:16,365 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-17 21:13:16,365 - websockets.server - INFO - server listening on [::1]:8001
2025-06-17 21:13:16,365 - RelayServer-relay_001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-17 21:13:16,365 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-17 21:13:16,365 - websockets.server - INFO - server listening on [::1]:8002
2025-06-17 21:13:16,365 - RelayServer-relay_002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-17 21:13:16,365 - websockets.server - INFO - server listening on [::1]:8003
2025-06-17 21:13:16,366 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-17 21:13:16,366 - RelayServer-relay_003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-17 21:14:30,990 - websockets.server - INFO - connection open
2025-06-17 21:14:30,991 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_b80b4761ae66642c from ('127.0.0.1', 61000)
2025-06-17 21:14:30,991 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_b80b4761ae66642c
2025-06-17 21:15:24,320 - websockets.server - INFO - connection open
2025-06-17 21:15:24,321 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_55bb0ce37cb94e24 from ('127.0.0.1', 61023)
2025-06-17 21:15:24,321 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_55bb0ce37cb94e24
2025-06-17 21:16:17,033 - RelayServer-relay_001 - INFO - ✓ 密钥交换请求已转发: aaa123 -> bbb123
2025-06-17 21:16:17,035 - RelayServer-relay_001 - INFO - ✓ 密钥交换响应已转发: bbb123 -> aaa123
2025-06-17 21:16:17,036 - RelayServer-relay_001 - INFO - ✓ 洋葱路由包已转发: 电路 circuit_bbb123_1750166177
2025-06-17 21:16:53,378 - RelayServer-relay_001 - INFO - ✓ 洋葱路由包已转发: 电路 circuit_aaa123_1750166213
2025-06-17 21:17:25,038 - RelayServer-relay_001 - INFO - 客户端断开连接: client_55bb0ce37cb94e24
2025-06-17 21:17:25,038 - RelayServer-relay_001 - INFO - 客户端已断开: client_55bb0ce37cb94e24 - Unknown
2025-06-17 21:17:34,447 - websockets.server - INFO - connection open
2025-06-17 21:17:34,447 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_a2ccbbe598b44e83 from ('127.0.0.1', 61084)
2025-06-17 21:17:34,447 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_a2ccbbe598b44e83
2025-06-17 21:18:16,374 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 3, 中继消息: 21, 传输字节: 2177
2025-06-17 21:18:16,374 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 21:18:16,374 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 21:21:07,180 - RelayServer-relay_001 - INFO - 客户端断开连接: client_b80b4761ae66642c
2025-06-17 21:21:07,180 - RelayServer-relay_001 - INFO - 客户端已断开: client_b80b4761ae66642c - Unknown
2025-06-17 21:21:14,806 - RelayServer-relay_001 - INFO - 客户端断开连接: client_a2ccbbe598b44e83
2025-06-17 21:21:14,806 - RelayServer-relay_001 - INFO - 客户端已断开: client_a2ccbbe598b44e83 - Unknown
2025-06-17 21:22:34,819 - websockets.server - INFO - connection open
2025-06-17 21:22:34,820 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_97b2377b08d8de45 from ('127.0.0.1', 61238)
2025-06-17 21:22:34,820 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: aaa123 -> client_97b2377b08d8de45
2025-06-17 21:22:57,553 - websockets.server - INFO - connection open
2025-06-17 21:22:57,553 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_040fc1de57327010 from ('127.0.0.1', 61259)
2025-06-17 21:22:57,554 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: bbb123 -> client_040fc1de57327010
2025-06-17 21:23:16,377 - RelayServer-relay_001 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 5, 中继消息: 36, 传输字节: 3343
2025-06-17 21:23:16,377 - RelayServer-relay_003 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 21:23:16,377 - RelayServer-relay_002 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 0, 中继消息: 0, 传输字节: 0
2025-06-17 21:23:40,402 - RelayServer-relay_001 - INFO - ✓ 密钥交换请求已转发: aaa123 -> bbb123
2025-06-17 21:23:40,403 - RelayServer-relay_001 - INFO - ✓ 密钥交换响应已转发: bbb123 -> aaa123
2025-06-17 21:23:40,404 - RelayServer-relay_001 - INFO - ✓ 洋葱路由包已转发: 电路 circuit_bbb123_1750166620
2025-06-17 21:24:52,467 - websockets.server - INFO - connection open
2025-06-17 21:24:52,467 - RelayServer-relay_001 - INFO - ✓ 客户端连接: client_a93b428a459586ac from ('127.0.0.1', 61345)
2025-06-17 21:24:52,470 - RelayServer-relay_001 - INFO - ✓ 用户注册成功: test_onion -> client_a93b428a459586ac
2025-06-17 21:24:52,471 - RelayServer-relay_001 - INFO - ✓ 洋葱路由包已转发: 电路 test_circuit_123
2025-06-19 01:48:39,133 - RelayServer-relay_5eff2022 - INFO - 启动中继服务器 relay_5eff2022 on localhost:8001
2025-06-19 01:48:39,143 - websockets.server - INFO - server listening on [::1]:8001
2025-06-19 01:48:39,143 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-19 01:48:39,143 - RelayServer-relay_5eff2022 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-19 01:49:48,684 - websockets.server - INFO - server closing
2025-06-19 01:49:48,686 - websockets.server - INFO - server closed
2025-06-19 01:49:48,686 - RelayServer-relay_5eff2022 - INFO - 正在停止中继服务器...
2025-06-19 01:49:48,686 - RelayServer-relay_5eff2022 - INFO - ✓ 中继服务器已停止
2025-06-19 02:23:50,251 - RelayServer-test_relay - INFO - 启动中继服务器 test_relay on localhost:8001
2025-06-19 02:23:50,267 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-19 02:23:50,267 - websockets.server - INFO - server listening on [::1]:8001
2025-06-19 02:23:50,268 - RelayServer-test_relay - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-19 02:23:52,267 - websockets.server - INFO - connection open
2025-06-19 02:23:52,267 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-19 02:23:52,267 - websockets.server - INFO - connection open
2025-06-19 02:23:52,267 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-19 02:23:52,273 - src.network.client - INFO - 连接已关闭: ws://localhost:8001
2025-06-19 02:23:52,274 - src.network.client - INFO - 连接已关闭: ws://localhost:8001
2025-06-19 02:23:52,275 - src.network.client - ERROR - 注册失败: received 1011 (internal error); then sent 1011 (internal error)
2025-06-19 02:23:52,275 - src.network.client - ERROR - 注册失败: received 1011 (internal error); then sent 1011 (internal error)
2025-06-19 02:24:02,271 - websockets.server - INFO - server closing
2025-06-19 02:24:02,271 - websockets.server - INFO - server closed
2025-06-19 02:24:02,272 - RelayServer-test_relay - INFO - 正在停止中继服务器...
2025-06-19 02:24:02,272 - RelayServer-test_relay - INFO - ✓ 中继服务器已停止
2025-06-19 02:24:44,812 - src.network.client - ERROR - 连接中继服务器失败: ws://localhost:8001, 错误: [WinError 1225] 远程计算机拒绝网络连接。
2025-06-19 02:31:53,825 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-19' coro=<AnonymousClient.stop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:85> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 02:31:53,827 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:133> wait_for=<Future cancelled>>
2025-06-19 02:31:53,827 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:83> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]>>
2025-06-19 02:31:53,828 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:31:53,828 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:31:53,828 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:133> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:31:53,829 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:133> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:31:53,829 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:31:53,836 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._connection_monitor_loop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:387> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:31:53,836 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:378> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:33:53,924 - src.network.client - INFO - 连接已关闭: ws://localhost:8011
2025-06-19 02:33:53,924 - src.network.client - INFO - 连接已关闭: ws://localhost:8011
2025-06-19 02:33:53,924 - src.network.client - INFO - 连接已关闭: ws://localhost:8013
2025-06-19 02:33:53,924 - src.network.client - INFO - 连接已关闭: ws://localhost:8012
2025-06-19 02:33:53,924 - src.network.client - INFO - 连接已关闭: ws://localhost:8012
2025-06-19 02:33:53,924 - src.network.client - INFO - 连接已关闭: ws://localhost:8013
2025-06-19 02:34:57,740 - RelayServer-relay_80b0c697 - INFO - 启动中继服务器 relay_80b0c697 on localhost:8001
2025-06-19 02:34:57,750 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-19 02:34:57,750 - websockets.server - INFO - server listening on [::1]:8001
2025-06-19 02:34:57,750 - RelayServer-relay_80b0c697 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-19 02:35:19,235 - RelayServer-relay_2b5adbf5 - INFO - 启动中继服务器 relay_2b5adbf5 on localhost:8002
2025-06-19 02:35:19,243 - websockets.server - INFO - server listening on [::1]:8002
2025-06-19 02:35:19,243 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-19 02:35:19,243 - RelayServer-relay_2b5adbf5 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-19 02:35:26,987 - RelayServer-relay_50ba7aba - INFO - 启动中继服务器 relay_50ba7aba on localhost:8003
2025-06-19 02:35:26,999 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-19 02:35:26,999 - websockets.server - INFO - server listening on [::1]:8003
2025-06-19 02:35:27,000 - RelayServer-relay_50ba7aba - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-19 02:35:43,051 - RelayServer-relay_8001 - INFO - 启动中继服务器 relay_8001 on localhost:8001
2025-06-19 02:35:43,051 - RelayServer-relay_8002 - INFO - 启动中继服务器 relay_8002 on localhost:8002
2025-06-19 02:35:43,051 - RelayServer-relay_8003 - INFO - 启动中继服务器 relay_8003 on localhost:8003
2025-06-19 02:35:43,067 - websockets.server - INFO - server listening on [::1]:8002
2025-06-19 02:35:43,067 - websockets.server - INFO - server listening on 127.0.0.1:8002
2025-06-19 02:35:43,067 - RelayServer-relay_8002 - INFO - ✓ 中继服务器已启动: ws://localhost:8002
2025-06-19 02:35:43,067 - websockets.server - INFO - server listening on [::1]:8003
2025-06-19 02:35:43,067 - websockets.server - INFO - server listening on 127.0.0.1:8003
2025-06-19 02:35:43,067 - RelayServer-relay_8003 - INFO - ✓ 中继服务器已启动: ws://localhost:8003
2025-06-19 02:35:43,067 - websockets.server - INFO - server listening on 127.0.0.1:8001
2025-06-19 02:35:43,067 - websockets.server - INFO - server listening on [::1]:8001
2025-06-19 02:35:43,067 - RelayServer-relay_8001 - INFO - ✓ 中继服务器已启动: ws://localhost:8001
2025-06-19 02:36:03,876 - src.network.client - ERROR - 连接中继服务器失败: ws://localhost:8011, 错误: [WinError 1225] 远程计算机拒绝网络连接。
2025-06-19 02:36:07,952 - src.network.client - ERROR - 连接中继服务器失败: ws://localhost:8012, 错误: [WinError 1225] 远程计算机拒绝网络连接。
2025-06-19 02:36:21,305 - websockets.server - INFO - server closing
2025-06-19 02:36:21,306 - websockets.server - INFO - server closing
2025-06-19 02:36:21,306 - websockets.server - INFO - server closing
2025-06-19 02:36:21,306 - websockets.server - INFO - server closed
2025-06-19 02:36:21,306 - websockets.server - INFO - server closed
2025-06-19 02:36:21,306 - websockets.server - INFO - server closed
2025-06-19 02:36:21,306 - RelayServer-relay_8001 - INFO - 正在停止中继服务器...
2025-06-19 02:36:21,307 - RelayServer-relay_8001 - INFO - ✓ 中继服务器已停止
2025-06-19 02:36:21,307 - RelayServer-relay_8002 - INFO - 正在停止中继服务器...
2025-06-19 02:36:21,307 - RelayServer-relay_8002 - INFO - ✓ 中继服务器已停止
2025-06-19 02:36:21,307 - RelayServer-relay_8003 - INFO - 正在停止中继服务器...
2025-06-19 02:36:21,307 - RelayServer-relay_8003 - INFO - ✓ 中继服务器已停止
2025-06-19 02:36:36,421 - RelayServer-relay_8011 - INFO - 启动中继服务器 relay_8011 on localhost:8011
2025-06-19 02:36:36,421 - RelayServer-relay_8012 - INFO - 启动中继服务器 relay_8012 on localhost:8012
2025-06-19 02:36:36,421 - RelayServer-relay_8013 - INFO - 启动中继服务器 relay_8013 on localhost:8013
2025-06-19 02:36:36,438 - websockets.server - INFO - server listening on 127.0.0.1:8012
2025-06-19 02:36:36,438 - websockets.server - INFO - server listening on [::1]:8012
2025-06-19 02:36:36,439 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已启动: ws://localhost:8012
2025-06-19 02:36:36,439 - websockets.server - INFO - server listening on 127.0.0.1:8011
2025-06-19 02:36:36,439 - websockets.server - INFO - server listening on [::1]:8011
2025-06-19 02:36:36,439 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已启动: ws://localhost:8011
2025-06-19 02:36:36,439 - websockets.server - INFO - server listening on [::1]:8013
2025-06-19 02:36:36,439 - websockets.server - INFO - server listening on 127.0.0.1:8013
2025-06-19 02:36:36,439 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已启动: ws://localhost:8013
2025-06-19 02:36:54,256 - websockets.server - INFO - connection open
2025-06-19 02:36:54,257 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-19 02:36:54,259 - src.network.client - INFO - 连接已关闭: ws://localhost:8011
2025-06-19 02:36:54,259 - websockets.server - INFO - connection open
2025-06-19 02:36:54,259 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-19 02:36:54,262 - src.network.client - INFO - 连接已关闭: ws://localhost:8012
2025-06-19 02:36:54,262 - websockets.server - INFO - connection open
2025-06-19 02:36:54,263 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: RelayServer.handle_client() missing 1 required positional argument: 'path'
2025-06-19 02:36:54,263 - src.network.client - ERROR - 注册失败: received 1011 (internal error); then sent 1011 (internal error)
2025-06-19 02:36:54,263 - src.network.client - ERROR - 注册失败: received 1011 (internal error); then sent 1011 (internal error)
2025-06-19 02:36:54,264 - src.network.client - INFO - 连接已关闭: ws://localhost:8013
2025-06-19 02:38:27,171 - RelayServer-relay_8011 - INFO - 启动中继服务器 relay_8011 on localhost:8011
2025-06-19 02:38:27,174 - RelayServer-relay_8012 - INFO - 启动中继服务器 relay_8012 on localhost:8012
2025-06-19 02:38:27,174 - RelayServer-relay_8013 - INFO - 启动中继服务器 relay_8013 on localhost:8013
2025-06-19 02:38:27,184 - websockets.server - INFO - server listening on [::1]:8012
2025-06-19 02:38:27,184 - websockets.server - INFO - server listening on 127.0.0.1:8012
2025-06-19 02:38:27,184 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已启动: ws://localhost:8012
2025-06-19 02:38:27,184 - websockets.server - INFO - server listening on [::1]:8011
2025-06-19 02:38:27,184 - websockets.server - INFO - server listening on 127.0.0.1:8011
2025-06-19 02:38:27,184 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已启动: ws://localhost:8011
2025-06-19 02:38:27,184 - websockets.server - INFO - server listening on 127.0.0.1:8013
2025-06-19 02:38:27,184 - websockets.server - INFO - server listening on [::1]:8013
2025-06-19 02:38:27,184 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已启动: ws://localhost:8013
2025-06-19 02:38:42,974 - websockets.server - INFO - connection open
2025-06-19 02:38:42,974 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: make_handler.<locals>.handler() missing 1 required positional argument: 'path'
2025-06-19 02:38:42,976 - websockets.server - INFO - connection open
2025-06-19 02:38:42,976 - src.network.client - INFO - 连接已关闭: ws://localhost:8011
2025-06-19 02:38:42,977 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: make_handler.<locals>.handler() missing 1 required positional argument: 'path'
2025-06-19 02:38:42,979 - src.network.client - INFO - 连接已关闭: ws://localhost:8012
2025-06-19 02:38:42,979 - websockets.server - INFO - connection open
2025-06-19 02:38:42,980 - src.network.client - ERROR - 注册失败: received 1011 (internal error); then sent 1011 (internal error)
2025-06-19 02:38:42,980 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "D:\anaconda\Lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: make_handler.<locals>.handler() missing 1 required positional argument: 'path'
2025-06-19 02:38:42,981 - src.network.client - ERROR - 注册失败: received 1011 (internal error); then sent 1011 (internal error)
2025-06-19 02:38:42,981 - src.network.client - INFO - 连接已关闭: ws://localhost:8013
2025-06-19 02:40:15,198 - websockets.server - INFO - server closing
2025-06-19 02:40:15,199 - websockets.server - INFO - server closing
2025-06-19 02:40:15,199 - websockets.server - INFO - server closing
2025-06-19 02:40:15,201 - websockets.server - INFO - server closed
2025-06-19 02:40:15,201 - websockets.server - INFO - server closed
2025-06-19 02:40:15,202 - websockets.server - INFO - server closed
2025-06-19 02:40:15,202 - RelayServer-relay_8011 - INFO - 正在停止中继服务器...
2025-06-19 02:40:15,202 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已停止
2025-06-19 02:40:15,202 - RelayServer-relay_8012 - INFO - 正在停止中继服务器...
2025-06-19 02:40:15,203 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已停止
2025-06-19 02:40:15,203 - RelayServer-relay_8013 - INFO - 正在停止中继服务器...
2025-06-19 02:40:15,203 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已停止
2025-06-19 02:40:19,997 - RelayServer-relay_8011 - INFO - 启动中继服务器 relay_8011 on localhost:8011
2025-06-19 02:40:20,002 - RelayServer-relay_8012 - INFO - 启动中继服务器 relay_8012 on localhost:8012
2025-06-19 02:40:20,002 - RelayServer-relay_8013 - INFO - 启动中继服务器 relay_8013 on localhost:8013
2025-06-19 02:40:20,013 - websockets.server - INFO - server listening on 127.0.0.1:8013
2025-06-19 02:40:20,014 - websockets.server - INFO - server listening on [::1]:8013
2025-06-19 02:40:20,014 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已启动: ws://localhost:8013
2025-06-19 02:40:20,014 - websockets.server - INFO - server listening on 127.0.0.1:8011
2025-06-19 02:40:20,014 - websockets.server - INFO - server listening on [::1]:8011
2025-06-19 02:40:20,014 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已启动: ws://localhost:8011
2025-06-19 02:40:20,014 - websockets.server - INFO - server listening on [::1]:8012
2025-06-19 02:40:20,015 - websockets.server - INFO - server listening on 127.0.0.1:8012
2025-06-19 02:40:20,015 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已启动: ws://localhost:8012
2025-06-19 02:40:38,596 - websockets.server - INFO - connection open
2025-06-19 02:40:38,596 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_5c6325a205737595 from ('127.0.0.1', 54448)
2025-06-19 02:40:38,596 - websockets.server - INFO - connection open
2025-06-19 02:40:38,596 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_769020777b3e8b70 from ('127.0.0.1', 54449)
2025-06-19 02:40:38,596 - websockets.server - INFO - connection open
2025-06-19 02:40:38,596 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_ae0b3b5fd9f07c9a from ('127.0.0.1', 54450)
2025-06-19 02:40:38,596 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_5c6325a205737595
2025-06-19 02:40:38,596 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_5c6325a205737595
2025-06-19 02:40:38,596 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_769020777b3e8b70
2025-06-19 02:40:38,596 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_769020777b3e8b70
2025-06-19 02:40:38,596 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_ae0b3b5fd9f07c9a
2025-06-19 02:40:38,596 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_ae0b3b5fd9f07c9a
2025-06-19 02:40:58,609 - websockets.server - INFO - connection open
2025-06-19 02:40:58,609 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_563abea26649ccd8 from ('127.0.0.1', 54464)
2025-06-19 02:40:58,612 - websockets.server - INFO - connection open
2025-06-19 02:40:58,612 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_2904cd231cfe4757 from ('127.0.0.1', 54465)
2025-06-19 02:40:58,612 - websockets.server - INFO - connection open
2025-06-19 02:40:58,617 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_4c5b99c49e7383dd from ('127.0.0.1', 54466)
2025-06-19 02:40:58,617 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_563abea26649ccd8
2025-06-19 02:40:58,617 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_563abea26649ccd8
2025-06-19 02:40:58,617 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_2904cd231cfe4757
2025-06-19 02:40:58,621 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_2904cd231cfe4757
2025-06-19 02:40:58,621 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_4c5b99c49e7383dd
2025-06-19 02:40:58,622 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_4c5b99c49e7383dd
2025-06-19 02:41:14,036 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xad in position 6: invalid start byte
2025-06-19 02:41:16,811 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_5c6325a205737595
2025-06-19 02:41:16,811 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_5c6325a205737595 -> bbb
2025-06-19 02:41:17,399 - src.network.client - ERROR - 处理握手初始化失败: Unable to load PEM file. See https://cryptography.io/en/latest/faq/#why-can-t-i-import-my-pem-file for more details. MalformedFraming
2025-06-19 02:41:24,019 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x90 in position 5: invalid start byte
2025-06-19 02:41:34,016 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb6 in position 5: invalid start byte
2025-06-19 02:41:46,024 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 7-8: invalid continuation byte
2025-06-19 02:41:47,019 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x95 in position 10: invalid start byte
2025-06-19 02:42:00,025 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb7 in position 8: invalid start byte
2025-06-19 02:42:03,030 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x97 in position 6: invalid start byte
2025-06-19 02:42:07,035 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9c in position 6: invalid start byte
2025-06-19 02:42:21,031 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf5 in position 6: invalid start byte
2025-06-19 02:42:22,032 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xcf in position 5: invalid continuation byte
2025-06-19 02:42:28,034 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbb in position 7: invalid start byte
2025-06-19 02:42:37,024 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xde in position 6: invalid continuation byte
2025-06-19 02:42:49,043 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbf in position 8: invalid start byte
2025-06-19 02:42:49,043 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xce in position 6: invalid continuation byte
2025-06-19 02:42:57,037 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9a in position 5: invalid start byte
2025-06-19 02:43:04,043 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa2 in position 7: invalid start byte
2025-06-19 02:43:20,043 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x85 in position 8: invalid start byte
2025-06-19 02:43:24,055 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd3 in position 11: invalid continuation byte
2025-06-19 02:43:36,069 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa4 in position 5: invalid start byte
2025-06-19 02:43:42,013 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_563abea26649ccd8
2025-06-19 02:43:42,013 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_563abea26649ccd8 -> aaa
2025-06-19 02:43:42,186 - src.network.client - ERROR - 处理握手初始化失败: Unable to load PEM file. See https://cryptography.io/en/latest/faq/#why-can-t-i-import-my-pem-file for more details. MalformedFraming
2025-06-19 02:43:43,045 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbe in position 7: invalid start byte
2025-06-19 02:43:45,046 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xec in position 5: invalid continuation byte
2025-06-19 02:43:51,094 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd6 in position 7: invalid continuation byte
2025-06-19 02:44:01,057 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9c in position 5: invalid start byte
2025-06-19 02:44:13,072 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x88 in position 13: invalid start byte
2025-06-19 02:44:18,091 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc9 in position 7: invalid continuation byte
2025-06-19 02:44:21,051 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd3 in position 6: invalid continuation byte
2025-06-19 02:44:28,104 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfd in position 6: invalid start byte
2025-06-19 02:44:36,081 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd4 in position 8: invalid continuation byte
2025-06-19 02:44:50,072 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb8 in position 8: invalid start byte
2025-06-19 02:44:52,120 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x96 in position 5: invalid start byte
2025-06-19 02:44:54,044 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc0 in position 8: invalid start byte
2025-06-19 02:45:20,013 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 02:45:20,014 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 02:45:20,014 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 301s, 活跃连接: 2, 总连接: 2, 中继消息: 4, 传输字节: 1880
2025-06-19 02:45:27,046 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc5 in position 6: invalid continuation byte
2025-06-19 02:45:27,128 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 5-6: invalid continuation byte
2025-06-19 02:45:29,071 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa0 in position 6: invalid start byte
2025-06-19 02:45:43,096 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x94 in position 9: invalid start byte
2025-06-19 02:45:45,205 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-20' coro=<AnonymousClient.stop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:86> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 02:45:45,206 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:134> wait_for=<Future cancelled>>
2025-06-19 02:45:45,207 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:134> wait_for=<Future cancelled>>
2025-06-19 02:45:45,207 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:134> wait_for=<Future cancelled>>
2025-06-19 02:45:45,207 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:78> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:45,207 - src.network.client - ERROR - 断开连接失败: ws://localhost:8011, 错误: no running event loop
2025-06-19 02:45:45,207 - src.network.client - ERROR - 断开连接失败: ws://localhost:8012, 错误: Event loop is closed
2025-06-19 02:45:45,207 - src.network.client - ERROR - 断开连接失败: ws://localhost:8013, 错误: Event loop is closed
2025-06-19 02:45:45,209 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:45,209 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_769020777b3e8b70
2025-06-19 02:45:45,209 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-19 02:45:45,209 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:45,209 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:45:45,209 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:45,209 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_ae0b3b5fd9f07c9a
2025-06-19 02:45:45,209 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-19 02:45:45,209 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:385> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:45,210 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:45:45,210 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._connection_monitor_loop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:394> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:45,210 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-19 02:45:45,210 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-16' coro=<AnonymousClient.stop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:86> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\anaconda\Lib\asyncio\tasks.py:898, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:134> wait_for=<Future cancelled>>
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:134> wait_for=<Future cancelled>>
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:134> wait_for=<Future cancelled>>
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:78> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:48,646 - src.network.client - ERROR - 断开连接失败: ws://localhost:8011, 错误: no running event loop
2025-06-19 02:45:48,646 - src.network.client - ERROR - 断开连接失败: ws://localhost:8012, 错误: Event loop is closed
2025-06-19 02:45:48,646 - src.network.client - ERROR - 断开连接失败: ws://localhost:8013, 错误: Event loop is closed
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:48,646 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_2904cd231cfe4757
2025-06-19 02:45:48,646 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:48,646 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\anaconda\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:48,646 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_4c5b99c49e7383dd
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:385> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:48,646 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-19 02:45:48,646 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:45:48,646 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._connection_monitor_loop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:394> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 02:45:48,646 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-19 02:45:48,646 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:46:00,039 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:46:06,130 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:46:08,260 - websockets.server - INFO - connection open
2025-06-19 02:46:08,260 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_24602a3beec2efc1 from ('127.0.0.1', 54910)
2025-06-19 02:46:08,262 - websockets.server - INFO - connection open
2025-06-19 02:46:08,262 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_c4ba1c65eb572fd1 from ('127.0.0.1', 54911)
2025-06-19 02:46:08,263 - websockets.server - INFO - connection open
2025-06-19 02:46:08,264 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_b3667e9e3a1a4039 from ('127.0.0.1', 54912)
2025-06-19 02:46:08,264 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_24602a3beec2efc1
2025-06-19 02:46:08,265 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_24602a3beec2efc1
2025-06-19 02:46:08,266 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_c4ba1c65eb572fd1
2025-06-19 02:46:08,267 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_c4ba1c65eb572fd1
2025-06-19 02:46:08,269 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_b3667e9e3a1a4039
2025-06-19 02:46:08,269 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_b3667e9e3a1a4039
2025-06-19 02:46:12,106 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-19 02:46:19,047 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:46:20,072 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:46:20,072 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:46:20,072 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 02:46:20,072 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:46:20,072 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:46:20,072 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 02:46:20,072 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:46:20,072 - RelayServer-relay_8011 - INFO - 清理了 1 个过期连接
2025-06-19 02:46:32,111 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x94 in position 5: invalid start byte
2025-06-19 02:46:32,356 - websockets.server - INFO - connection open
2025-06-19 02:46:32,357 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_320737bfffd01e21 from ('127.0.0.1', 54926)
2025-06-19 02:46:32,358 - websockets.server - INFO - connection open
2025-06-19 02:46:32,359 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_8210e09d687948b3 from ('127.0.0.1', 54927)
2025-06-19 02:46:32,360 - websockets.server - INFO - connection open
2025-06-19 02:46:32,360 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_8e832761bab1a71d from ('127.0.0.1', 54928)
2025-06-19 02:46:32,360 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_320737bfffd01e21
2025-06-19 02:46:32,361 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_320737bfffd01e21
2025-06-19 02:46:32,361 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_8210e09d687948b3
2025-06-19 02:46:32,362 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_8210e09d687948b3
2025-06-19 02:46:32,362 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_8e832761bab1a71d
2025-06-19 02:46:32,362 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_8e832761bab1a71d
2025-06-19 02:46:37,138 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:46:48,057 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9a in position 13: invalid start byte
2025-06-19 02:46:50,165 - src.network.client - ERROR - 添加联系人失败: a bytes-like object is required, not 'str'
2025-06-19 02:47:00,077 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:47:06,141 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:47:08,116 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: received 1000 (OK); then sent 1000 (OK)
2025-06-19 02:47:20,077 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:20,077 - RelayServer-relay_8011 - INFO - 清理了 1 个过期连接
2025-06-19 02:47:20,077 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:20,077 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:20,077 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 02:47:20,077 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:20,077 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:20,077 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 02:47:24,083 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 02:47:34,131 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8e in position 5: invalid start byte
2025-06-19 02:47:42,143 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa1 in position 5: invalid start byte
2025-06-19 02:47:45,097 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfb in position 5: invalid start byte
2025-06-19 02:47:46,586 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_320737bfffd01e21
2025-06-19 02:47:46,586 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-19 02:47:46,586 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:46,586 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_8210e09d687948b3
2025-06-19 02:47:46,586 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-19 02:47:46,586 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:46,586 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_8e832761bab1a71d
2025-06-19 02:47:46,586 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-19 02:47:46,586 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 02:47:53,694 - src.network.client - INFO - 连接已关闭: ws://localhost:8013
2025-06-19 02:47:53,709 - src.network.client - INFO - 连接已关闭: ws://localhost:8012
2025-06-19 02:47:53,709 - src.network.client - INFO - 连接已关闭: ws://localhost:8011
2025-06-19 18:02:14,795 - RelayServer-relay_8011 - INFO - 启动中继服务器 relay_8011 on localhost:8011
2025-06-19 18:02:14,800 - RelayServer-relay_8012 - INFO - 启动中继服务器 relay_8012 on localhost:8012
2025-06-19 18:02:14,804 - RelayServer-relay_8013 - INFO - 启动中继服务器 relay_8013 on localhost:8013
2025-06-19 18:02:14,816 - websockets.server - INFO - server listening on [::1]:8012
2025-06-19 18:02:14,816 - websockets.server - INFO - server listening on 127.0.0.1:8012
2025-06-19 18:02:14,820 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已启动: ws://localhost:8012
2025-06-19 18:02:14,821 - websockets.server - INFO - server listening on 127.0.0.1:8013
2025-06-19 18:02:14,821 - websockets.server - INFO - server listening on [::1]:8013
2025-06-19 18:02:14,821 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已启动: ws://localhost:8013
2025-06-19 18:02:14,821 - websockets.server - INFO - server listening on 127.0.0.1:8011
2025-06-19 18:02:14,825 - websockets.server - INFO - server listening on [::1]:8011
2025-06-19 18:02:14,825 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已启动: ws://localhost:8011
2025-06-19 18:02:36,864 - websockets.server - INFO - connection open
2025-06-19 18:02:36,864 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_07e4a90a4b302ff9 from ('127.0.0.1', 53730)
2025-06-19 18:02:36,869 - websockets.server - INFO - connection open
2025-06-19 18:02:36,869 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_04ecd4b75a0c8358 from ('127.0.0.1', 53731)
2025-06-19 18:02:36,873 - websockets.server - INFO - connection open
2025-06-19 18:02:36,876 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_72087efdf5487a94 from ('127.0.0.1', 53732)
2025-06-19 18:02:36,876 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_07e4a90a4b302ff9
2025-06-19 18:02:36,876 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_07e4a90a4b302ff9
2025-06-19 18:02:36,881 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_04ecd4b75a0c8358
2025-06-19 18:02:36,881 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_04ecd4b75a0c8358
2025-06-19 18:02:36,881 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_72087efdf5487a94
2025-06-19 18:02:36,881 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_72087efdf5487a94
2025-06-19 18:03:06,662 - websockets.server - INFO - connection open
2025-06-19 18:03:06,662 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_58f08e32c3aa29ff from ('127.0.0.1', 53752)
2025-06-19 18:03:06,666 - websockets.server - INFO - connection open
2025-06-19 18:03:06,666 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_f7e985cc33e11a99 from ('127.0.0.1', 53753)
2025-06-19 18:03:06,670 - websockets.server - INFO - connection open
2025-06-19 18:03:06,674 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_35254744ec3f8a2e from ('127.0.0.1', 53754)
2025-06-19 18:03:06,674 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_58f08e32c3aa29ff
2025-06-19 18:03:06,674 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_58f08e32c3aa29ff
2025-06-19 18:03:06,674 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_f7e985cc33e11a99
2025-06-19 18:03:06,674 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_f7e985cc33e11a99
2025-06-19 18:03:06,674 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_35254744ec3f8a2e
2025-06-19 18:03:06,674 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_35254744ec3f8a2e
2025-06-19 18:03:12,853 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x88 in position 5: invalid start byte
2025-06-19 18:03:17,563 - src.network.client - ERROR - 添加联系人失败: a bytes-like object is required, not 'str'
2025-06-19 18:03:19,239 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xff in position 5: invalid start byte
2025-06-19 18:03:24,858 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfd in position 6: invalid start byte
2025-06-19 18:03:25,844 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe9 in position 5: invalid continuation byte
2025-06-19 18:03:36,865 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb6 in position 5: invalid start byte
2025-06-19 18:03:45,863 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa1 in position 6: invalid start byte
2025-06-19 18:03:46,835 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd0 in position 8: invalid continuation byte
2025-06-19 18:03:56,847 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8b in position 8: invalid start byte
2025-06-19 18:04:00,843 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe0 in position 6: invalid continuation byte
2025-06-19 18:04:07,878 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 6-7: invalid continuation byte
2025-06-19 18:04:19,858 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8b in position 5: invalid start byte
2025-06-19 18:04:27,879 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xcd in position 5: invalid continuation byte
2025-06-19 18:04:38,848 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf4 in position 5: invalid continuation byte
2025-06-19 18:04:46,859 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x98 in position 7: invalid start byte
2025-06-19 18:05:01,877 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x92 in position 6: invalid start byte
2025-06-19 18:05:13,850 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfa in position 5: invalid start byte
2025-06-19 18:05:14,874 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa4 in position 5: invalid start byte
2025-06-19 18:05:15,886 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa7 in position 7: invalid start byte
2025-06-19 18:05:25,881 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9b in position 5: invalid start byte
2025-06-19 18:05:38,861 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe6 in position 8: invalid continuation byte
2025-06-19 18:05:47,878 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xab in position 10: invalid start byte
2025-06-19 18:05:52,902 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe3 in position 6: invalid continuation byte
2025-06-19 18:05:58,866 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9d in position 5: invalid start byte
2025-06-19 18:06:13,909 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xba in position 5: invalid start byte
2025-06-19 18:06:20,875 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x89 in position 6: invalid start byte
2025-06-19 18:06:25,880 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe2 in position 6: invalid continuation byte
2025-06-19 18:06:30,919 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x87 in position 5: invalid start byte
2025-06-19 18:06:39,882 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfd in position 8: invalid start byte
2025-06-19 18:06:42,924 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc8 in position 5: invalid continuation byte
2025-06-19 18:06:56,889 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x86 in position 5: invalid start byte
2025-06-19 18:07:02,899 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfb in position 8: invalid start byte
2025-06-19 18:07:02,899 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xab in position 7: invalid start byte
2025-06-19 18:07:14,832 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:07:14,832 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:07:14,832 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:07:22,891 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8f in position 5: invalid start byte
2025-06-19 18:07:24,920 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xcf in position 8: invalid continuation byte
2025-06-19 18:07:34,907 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd1 in position 7: invalid continuation byte
2025-06-19 18:07:35,896 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb8 in position 7: invalid start byte
2025-06-19 18:07:50,889 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xed in position 5: invalid continuation byte
2025-06-19 18:07:51,912 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbb in position 5: invalid start byte
2025-06-19 18:07:52,925 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf3 in position 5: invalid continuation byte
2025-06-19 18:08:08,975 - src.network.client - ERROR - 添加联系人失败: a bytes-like object is required, not 'str'
2025-06-19 18:08:14,865 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-19 18:08:14,865 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:08:14,865 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-19 18:08:14,865 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:08:14,865 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:08:14,865 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-19 18:08:14,869 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:08:14,869 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-19 18:08:14,869 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:08:14,869 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:08:14,869 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-19 18:08:14,869 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:08:14,869 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-19 18:08:14,869 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:08:14,869 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:08:18,966 - src.network.client - ERROR - 添加联系人失败: a bytes-like object is required, not 'str'
2025-06-19 18:08:21,927 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xba in position 6: invalid start byte
2025-06-19 18:08:29,903 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x89 in position 5: invalid start byte
2025-06-19 18:08:31,923 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x84 in position 9: invalid start byte
2025-06-19 18:08:52,942 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9e in position 5: invalid start byte
2025-06-19 18:08:57,909 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x95 in position 5: invalid start byte
2025-06-19 18:09:05,937 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 5-6: invalid continuation byte
2025-06-19 18:09:06,939 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xff in position 8: invalid start byte
2025-06-19 18:09:14,870 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:09:14,870 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:09:14,878 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:09:14,882 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:09:14,882 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:09:14,882 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:09:14,886 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:09:14,894 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:09:14,894 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:09:27,917 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd9 in position 6: invalid continuation byte
2025-06-19 18:09:38,949 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf6 in position 5: invalid start byte
2025-06-19 18:09:39,943 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9d in position 5: invalid start byte
2025-06-19 18:09:58,928 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbf in position 5: invalid start byte
2025-06-19 18:10:01,957 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe6 in position 7: invalid continuation byte
2025-06-19 18:10:14,895 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:10:14,895 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:10:14,895 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:10:14,899 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:10:14,899 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:10:14,899 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:10:14,903 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:10:14,903 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:10:14,903 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:10:14,951 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd1 in position 5: invalid continuation byte
2025-06-19 18:10:25,931 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb6 in position 6: invalid start byte
2025-06-19 18:10:30,970 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb5 in position 5: invalid start byte
2025-06-19 18:10:42,962 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc1 in position 5: invalid start byte
2025-06-19 18:10:49,956 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb5 in position 6: invalid start byte
2025-06-19 18:11:06,963 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf0 in position 6: invalid continuation byte
2025-06-19 18:11:13,982 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb9 in position 9: invalid start byte
2025-06-19 18:11:14,909 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:11:14,911 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:11:14,911 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:11:14,911 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:11:14,911 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:11:14,914 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:11:14,922 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:11:14,922 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:11:14,922 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:11:19,952 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa7 in position 5: invalid start byte
2025-06-19 18:11:29,988 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x85 in position 6: invalid start byte
2025-06-19 18:11:38,966 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe3 in position 9: invalid continuation byte
2025-06-19 18:11:44,988 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc3 in position 5: invalid continuation byte
2025-06-19 18:11:47,991 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe3 in position 7: invalid continuation byte
2025-06-19 18:12:01,973 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xdd in position 5: invalid continuation byte
2025-06-19 18:12:14,828 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:12:14,829 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:12:14,829 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:12:14,925 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:12:14,925 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:12:14,925 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:12:14,925 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:12:14,925 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:12:14,929 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:12:14,929 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:12:14,929 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:12:14,929 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:12:16,992 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc3 in position 6: invalid continuation byte
2025-06-19 18:12:21,006 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x80 in position 6: invalid start byte
2025-06-19 18:12:35,005 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa5 in position 5: invalid start byte
2025-06-19 18:12:35,979 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc5 in position 5: invalid continuation byte
2025-06-19 18:12:49,023 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa5 in position 5: invalid start byte
2025-06-19 18:13:03,013 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa4 in position 12: invalid start byte
2025-06-19 18:13:04,994 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x98 in position 7: invalid start byte
2025-06-19 18:13:14,929 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:13:14,931 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:13:14,938 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:13:14,938 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:13:14,938 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:13:14,941 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:13:14,941 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:13:14,941 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:13:14,941 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:13:25,012 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 7-8: invalid continuation byte
2025-06-19 18:13:25,035 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9f in position 9: invalid start byte
2025-06-19 18:13:34,016 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xdc in position 5: invalid continuation byte
2025-06-19 18:13:46,032 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe6 in position 5: invalid continuation byte
2025-06-19 18:13:49,016 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa1 in position 6: invalid start byte
2025-06-19 18:13:52,028 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8f in position 5: invalid start byte
2025-06-19 18:14:04,031 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x90 in position 9: invalid start byte
2025-06-19 18:14:07,035 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfb in position 7: invalid start byte
2025-06-19 18:14:14,943 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:14:14,943 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:14:14,943 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:14:14,943 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:14:14,943 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:14:14,943 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:14:14,943 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:14:14,943 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:14:14,943 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:14:18,035 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc3 in position 6: invalid continuation byte
2025-06-19 18:14:23,049 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc0 in position 5: invalid start byte
2025-06-19 18:14:23,049 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 5-6: invalid continuation byte
2025-06-19 18:14:29,030 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8c in position 7: invalid start byte
2025-06-19 18:14:52,033 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9d in position 6: invalid start byte
2025-06-19 18:14:52,033 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfb in position 5: invalid start byte
2025-06-19 18:14:54,061 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x84 in position 5: invalid start byte
2025-06-19 18:15:10,797 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_f7e985cc33e11a99
2025-06-19 18:15:10,797 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:10,799 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_58f08e32c3aa29ff
2025-06-19 18:15:10,799 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:10,799 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_35254744ec3f8a2e
2025-06-19 18:15:10,799 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:13,076 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:15:14,960 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:14,960 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:14,960 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:15:14,960 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:14,960 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:14,964 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:15:14,964 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:14,964 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:15:14,964 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:15:27,034 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8f in position 5: invalid start byte
2025-06-19 18:15:30,044 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:15:48,088 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:15:58,052 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:15:59,029 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe3 in position 6: invalid continuation byte
2025-06-19 18:16:12,104 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:16:14,978 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:16:14,978 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:16:14,978 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:16:14,978 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:16:14,978 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:16:14,978 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:16:14,978 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:16:14,978 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:16:14,978 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:16:23,037 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:16:24,057 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:16:33,117 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:16:41,047 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9c in position 5: invalid start byte
2025-06-19 18:16:55,051 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:16:57,072 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:17:10,124 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xef in position 5: invalid continuation byte
2025-06-19 18:17:14,058 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:17:14,834 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:17:14,834 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:17:14,834 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-19 18:17:14,988 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:17:14,989 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:17:14,989 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:17:14,989 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:17:14,989 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:17:14,989 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:17:14,989 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:17:14,989 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:17:14,989 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:17:25,126 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:17:30,078 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xae in position 15: invalid start byte
2025-06-19 18:17:35,085 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:17:51,102 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb4 in position 5: invalid start byte
2025-06-19 18:17:58,136 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa7 in position 5: invalid start byte
2025-06-19 18:18:08,071 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xcf in position 5: invalid continuation byte
2025-06-19 18:18:14,991 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:18:14,991 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:18:14,991 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:18:14,991 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:18:14,991 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:18:14,991 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:18:14,995 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:18:14,995 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:18:14,995 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:18:23,149 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xaa in position 6: invalid start byte
2025-06-19 18:18:26,117 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf1 in position 6: invalid continuation byte
2025-06-19 18:18:36,082 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe9 in position 8: invalid continuation byte
2025-06-19 18:18:53,170 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb9 in position 8: invalid start byte
2025-06-19 18:18:59,126 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf6 in position 7: invalid start byte
2025-06-19 18:19:05,093 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:19:10,130 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:19:14,991 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:19:14,991 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:19:14,991 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:19:14,991 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:19:14,991 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:19:14,991 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:19:14,994 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:19:14,994 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:19:14,994 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:19:19,172 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xad in position 5: invalid start byte
2025-06-19 18:19:26,101 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb3 in position 11: invalid start byte
2025-06-19 18:19:38,149 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb2 in position 10: invalid start byte
2025-06-19 18:19:43,179 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x89 in position 5: invalid start byte
2025-06-19 18:19:46,116 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa8 in position 5: invalid start byte
2025-06-19 18:20:00,169 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe8 in position 6: invalid continuation byte
2025-06-19 18:20:09,124 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf0 in position 5: invalid continuation byte
2025-06-19 18:20:10,201 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xac in position 7: invalid start byte
2025-06-19 18:20:15,002 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:15,002 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:15,002 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:20:15,002 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:15,006 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:15,006 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:20:15,006 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:15,007 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:15,007 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:20:27,178 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:20:28,198 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:20:37,133 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:20:39,068 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_07e4a90a4b302ff9
2025-06-19 18:20:39,068 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:39,068 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_04ecd4b75a0c8358
2025-06-19 18:20:39,068 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:39,068 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_72087efdf5487a94
2025-06-19 18:20:39,068 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:20:43,193 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:20:50,931 - websockets.server - INFO - connection open
2025-06-19 18:20:50,935 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_deff75d316dcd052 from ('127.0.0.1', 55202)
2025-06-19 18:20:50,936 - websockets.server - INFO - connection open
2025-06-19 18:20:50,936 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_3f4f71a1a0081357 from ('127.0.0.1', 55203)
2025-06-19 18:20:50,941 - websockets.server - INFO - connection open
2025-06-19 18:20:50,944 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_24a61c3abbf3dd29 from ('127.0.0.1', 55204)
2025-06-19 18:20:50,944 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_deff75d316dcd052
2025-06-19 18:20:50,944 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_deff75d316dcd052
2025-06-19 18:20:50,944 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_3f4f71a1a0081357
2025-06-19 18:20:50,944 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_3f4f71a1a0081357
2025-06-19 18:20:50,944 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_24a61c3abbf3dd29
2025-06-19 18:20:50,944 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_24a61c3abbf3dd29
2025-06-19 18:20:51,145 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:21:02,220 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:21:12,148 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:21:13,103 - websockets.server - INFO - connection open
2025-06-19 18:21:13,103 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_00060b9825518e71 from ('127.0.0.1', 55249)
2025-06-19 18:21:13,112 - websockets.server - INFO - connection open
2025-06-19 18:21:13,112 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_600b11cc29dfe6c5 from ('127.0.0.1', 55250)
2025-06-19 18:21:13,115 - websockets.server - INFO - connection open
2025-06-19 18:21:13,115 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_924beb7f446c63b8 from ('127.0.0.1', 55251)
2025-06-19 18:21:13,116 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_00060b9825518e71
2025-06-19 18:21:13,116 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_00060b9825518e71
2025-06-19 18:21:13,117 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_600b11cc29dfe6c5
2025-06-19 18:21:13,117 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_600b11cc29dfe6c5
2025-06-19 18:21:13,117 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_924beb7f446c63b8
2025-06-19 18:21:13,117 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_924beb7f446c63b8
2025-06-19 18:21:14,996 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:21:14,998 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:21:14,998 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:21:15,022 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:21:15,022 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:21:15,022 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:21:15,026 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:21:15,026 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:21:15,026 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:21:21,208 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe7 in position 5: invalid continuation byte
2025-06-19 18:21:39,212 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb3 in position 7: invalid start byte
2025-06-19 18:21:40,224 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x88 in position 5: invalid start byte
2025-06-19 18:21:43,164 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:21:52,245 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:21:57,279 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:21:57,279 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:21:57,352 - src.network.client - ERROR - 处理握手初始化失败: a bytes-like object is required, not 'str'
2025-06-19 18:22:13,232 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb2 in position 7: invalid start byte
2025-06-19 18:22:14,084 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:22:14,084 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:22:14,133 - src.network.client - ERROR - 处理握手初始化失败: a bytes-like object is required, not 'str'
2025-06-19 18:22:14,835 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 4, 总连接: 4, 中继消息: 6, 传输字节: 2006
2025-06-19 18:22:14,835 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 4, 总连接: 4, 中继消息: 4, 传输字节: 252
2025-06-19 18:22:14,835 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 4, 总连接: 4, 中继消息: 4, 传输字节: 252
2025-06-19 18:22:14,993 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:22:14,993 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:22:14,993 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:22:15,038 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:22:15,038 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:22:15,038 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:22:15,038 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:22:15,038 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:22:15,038 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:22:15,166 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:22:20,933 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:22:20,933 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:22:22,245 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x93 in position 7: invalid start byte
2025-06-19 18:22:27,233 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd6 in position 5: invalid continuation byte
2025-06-19 18:22:31,183 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb2 in position 5: invalid start byte
2025-06-19 18:22:43,107 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:22:43,107 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:22:43,235 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:22:50,940 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:22:50,940 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:22:54,248 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb4 in position 9: invalid start byte
2025-06-19 18:22:57,250 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd5 in position 10: invalid continuation byte
2025-06-19 18:23:02,202 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:23:13,104 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:23:13,108 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:23:13,205 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbb in position 6: invalid start byte
2025-06-19 18:23:15,017 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:23:15,017 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:23:15,017 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:23:15,049 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:23:15,049 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:23:15,049 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:23:15,049 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:23:15,049 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:23:15,049 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:23:20,948 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:23:20,948 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:23:26,257 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x95 in position 7: invalid start byte
2025-06-19 18:23:32,247 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb9 in position 7: invalid start byte
2025-06-19 18:23:39,212 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb2 in position 6: invalid start byte
2025-06-19 18:23:43,116 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:23:43,116 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:23:50,947 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:23:50,947 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:23:56,265 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:24:02,258 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:24:09,228 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x8d in position 7: invalid start byte
2025-06-19 18:24:13,113 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:24:13,113 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:24:15,026 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:24:15,026 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:24:15,026 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:24:15,026 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:24:15,026 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:24:15,028 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:24:15,028 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:24:15,030 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:24:15,030 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:24:20,964 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:24:20,964 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:24:25,270 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbe in position 5: invalid start byte
2025-06-19 18:24:28,245 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:24:30,264 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd1 in position 7: invalid continuation byte
2025-06-19 18:24:43,114 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:24:43,114 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:24:44,273 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:24:50,968 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:24:50,968 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:24:58,259 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:25:00,249 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x95 in position 7: invalid start byte
2025-06-19 18:25:10,286 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:25:13,099 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:25:13,101 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:25:15,031 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:25:15,032 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:25:15,032 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:25:15,032 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:25:15,032 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:25:15,032 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-19 18:25:15,032 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:25:15,035 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:25:15,035 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-19 18:25:17,253 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfa in position 8: invalid start byte
2025-06-19 18:25:17,280 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf8 in position 5: invalid start byte
2025-06-19 18:25:20,975 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:25:20,975 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:25:21,281 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x85 in position 6: invalid start byte
2025-06-19 18:25:29,254 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa9 in position 9: invalid start byte
2025-06-19 18:25:32,287 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:25:43,114 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:25:43,116 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:25:45,293 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:25:48,255 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 6-7: invalid continuation byte
2025-06-19 18:25:49,287 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xdf in position 5: invalid continuation byte
2025-06-19 18:25:50,967 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:25:50,967 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:26:08,305 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:26:10,297 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc9 in position 5: invalid continuation byte
2025-06-19 18:26:13,113 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_00060b9825518e71
2025-06-19 18:26:13,113 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_00060b9825518e71 -> aaa
2025-06-19 18:26:15,031 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,031 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,034 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-19 18:26:15,034 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,034 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-19 18:26:15,034 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,038 - RelayServer-relay_8012 - INFO - 清理了 4 个过期连接
2025-06-19 18:26:15,038 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,038 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,042 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:26:15,050 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,050 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,054 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-19 18:26:15,054 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,054 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-19 18:26:15,054 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:15,054 - RelayServer-relay_8013 - INFO - 清理了 4 个过期连接
2025-06-19 18:26:20,298 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 8-9: invalid continuation byte
2025-06-19 18:26:20,974 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_deff75d316dcd052
2025-06-19 18:26:20,974 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_deff75d316dcd052 -> bbb
2025-06-19 18:26:24,263 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:26:34,926 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_3f4f71a1a0081357
2025-06-19 18:26:34,926 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,930 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:34,930 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:135> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,930 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_24a61c3abbf3dd29
2025-06-19 18:26:34,930 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,930 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:34,934 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_deff75d316dcd052
2025-06-19 18:26:34,934 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-19 18:26:34,934 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:34,946 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:79> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,946 - src.network.client - ERROR - 断开连接失败: ws://localhost:8011, 错误: Event loop is closed
2025-06-19 18:26:34,946 - src.network.client - ERROR - 断开连接失败: ws://localhost:8012, 错误: Event loop is closed
2025-06-19 18:26:34,951 - src.network.client - ERROR - 断开连接失败: ws://localhost:8013, 错误: Event loop is closed
2025-06-19 18:26:34,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:135> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,958 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,970 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:135> wait_for=<Future cancelled>>
2025-06-19 18:26:34,970 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:389> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,970 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:403> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:34,979 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-20' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:87> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 18:26:38,322 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:26:41,135 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<AnonymousClient.start() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:79> wait_for=<Future finished result=None>>
2025-06-19 18:26:41,135 - src.network.client - ERROR - 断开连接失败: ws://localhost:8011, 错误: Event loop is closed
2025-06-19 18:26:41,139 - src.network.client - ERROR - 断开连接失败: ws://localhost:8012, 错误: Event loop is closed
2025-06-19 18:26:41,139 - src.network.client - ERROR - 断开连接失败: ws://localhost:8013, 错误: Event loop is closed
2025-06-19 18:26:41,139 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,139 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_600b11cc29dfe6c5
2025-06-19 18:26:41,139 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:41,143 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_924beb7f446c63b8
2025-06-19 18:26:41,143 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:41,143 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_00060b9825518e71
2025-06-19 18:26:41,143 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-19 18:26:41,143 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:26:41,139 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:135> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,147 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<Connection.keepalive() running at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,155 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-4' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:135> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,179 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<Connection.keepalive() done, defined at D:\警大\学科课程\数据安全技术\obe\venv\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,187 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-8' coro=<AnonymousClient._handle_relay_messages() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:135> wait_for=<Future cancelled>>
2025-06-19 18:26:41,191 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-9' coro=<AnonymousClient._heartbeat_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:389> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,191 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-10' coro=<AnonymousClient._connection_monitor_loop() done, defined at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:403> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-19 18:26:41,195 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-21' coro=<AnonymousClient.stop() running at D:\警大\学科课程\数据安全技术\obe\src\network\client.py:87> cb=[_chain_future.<locals>._call_set_state() at D:\anaconda\Lib\asyncio\futures.py:394]>
2025-06-19 18:26:47,279 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:26:52,307 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:08,339 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:14,281 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:14,848 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 4, 总连接: 4, 中继消息: 4, 传输字节: 252
2025-06-19 18:27:14,849 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 4, 总连接: 4, 中继消息: 23, 传输字节: 7735
2025-06-19 18:27:14,849 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 4, 总连接: 4, 中继消息: 4, 传输字节: 252
2025-06-19 18:27:15,048 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,048 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,048 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:27:15,048 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,048 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,052 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,052 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,052 - RelayServer-relay_8012 - INFO - 清理了 4 个过期连接
2025-06-19 18:27:15,052 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,052 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,052 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,056 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:27:15,057 - RelayServer-relay_8013 - INFO - 清理了 4 个过期连接
2025-06-19 18:27:30,311 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:43,313 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:46,295 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:46,343 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:27:54,319 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:28:04,346 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:28:15,054 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,055 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,055 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:28:15,055 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,055 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,055 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,055 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,058 - RelayServer-relay_8013 - INFO - 清理了 4 个过期连接
2025-06-19 18:28:15,058 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,058 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,058 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,058 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:28:15,058 - RelayServer-relay_8012 - INFO - 清理了 4 个过期连接
2025-06-19 18:28:24,307 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:28:31,329 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:28:37,351 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:28:38,316 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:28:45,340 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:01,359 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:11,314 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:11,359 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:13,377 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:15,076 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:29:15,076 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8012 - INFO - 清理了 4 个过期连接
2025-06-19 18:29:15,076 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:29:15,076 - RelayServer-relay_8013 - INFO - 清理了 4 个过期连接
2025-06-19 18:29:22,363 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:31,380 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:35,312 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:56,371 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:29:59,389 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:30:03,323 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:30:09,373 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-19 18:30:15,079 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,080 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,080 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-19 18:30:15,080 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,080 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,080 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,080 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,083 - RelayServer-relay_8013 - INFO - 清理了 4 个过期连接
2025-06-19 18:30:15,083 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,083 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,083 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,086 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-19 18:30:15,086 - RelayServer-relay_8012 - INFO - 清理了 4 个过期连接
2025-06-19 18:30:17,337 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:29:32,486 - RelayServer-relay_8011 - INFO - 启动中继服务器 relay_8011 on localhost:8011
2025-06-20 10:29:32,486 - RelayServer-relay_8012 - INFO - 启动中继服务器 relay_8012 on localhost:8012
2025-06-20 10:29:32,486 - RelayServer-relay_8013 - INFO - 启动中继服务器 relay_8013 on localhost:8013
2025-06-20 10:29:32,507 - websockets.server - INFO - server listening on 127.0.0.1:8013
2025-06-20 10:29:32,507 - websockets.server - INFO - server listening on [::1]:8013
2025-06-20 10:29:32,507 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已启动: ws://localhost:8013
2025-06-20 10:29:32,507 - websockets.server - INFO - server listening on 127.0.0.1:8011
2025-06-20 10:29:32,507 - websockets.server - INFO - server listening on [::1]:8011
2025-06-20 10:29:32,507 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已启动: ws://localhost:8011
2025-06-20 10:29:32,507 - websockets.server - INFO - server listening on [::1]:8012
2025-06-20 10:29:32,507 - websockets.server - INFO - server listening on 127.0.0.1:8012
2025-06-20 10:29:32,507 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已启动: ws://localhost:8012
2025-06-20 10:29:57,651 - websockets.server - INFO - connection open
2025-06-20 10:29:57,652 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_58a52e5e41090f7e from ('127.0.0.1', 50449)
2025-06-20 10:29:57,652 - websockets.server - INFO - connection open
2025-06-20 10:29:57,652 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_fb9b7c825e0c34c8 from ('127.0.0.1', 50450)
2025-06-20 10:29:57,652 - websockets.server - INFO - connection open
2025-06-20 10:29:57,652 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_a4098ac9545b079c from ('127.0.0.1', 50451)
2025-06-20 10:29:57,660 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_58a52e5e41090f7e
2025-06-20 10:29:57,660 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_58a52e5e41090f7e
2025-06-20 10:29:57,660 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_fb9b7c825e0c34c8
2025-06-20 10:29:57,660 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_fb9b7c825e0c34c8
2025-06-20 10:29:57,665 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_a4098ac9545b079c
2025-06-20 10:29:57,665 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_a4098ac9545b079c
2025-06-20 10:30:21,097 - websockets.server - INFO - connection open
2025-06-20 10:30:21,097 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_36112f92acf7836d from ('127.0.0.1', 50482)
2025-06-20 10:30:21,103 - websockets.server - INFO - connection open
2025-06-20 10:30:21,103 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_77da0afa995b14b7 from ('127.0.0.1', 50483)
2025-06-20 10:30:21,108 - websockets.server - INFO - connection open
2025-06-20 10:30:21,108 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_01fa3f1268518f24 from ('127.0.0.1', 50484)
2025-06-20 10:30:21,108 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_36112f92acf7836d
2025-06-20 10:30:21,108 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_36112f92acf7836d
2025-06-20 10:30:21,111 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_77da0afa995b14b7
2025-06-20 10:30:21,111 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_77da0afa995b14b7
2025-06-20 10:30:21,112 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_01fa3f1268518f24
2025-06-20 10:30:21,112 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_01fa3f1268518f24
2025-06-20 10:30:23,537 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x87 in position 5: invalid start byte
2025-06-20 10:30:27,546 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb1 in position 6: invalid start byte
2025-06-20 10:30:32,454 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_36112f92acf7836d
2025-06-20 10:30:32,454 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_36112f92acf7836d -> aaa
2025-06-20 10:30:32,471 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:30:32,472 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'b55015dd5e71e056', 'timestamp': 1750386632}
2025-06-20 10:30:32,472 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'b55015dd5e71e056', 'timestamp': 1750386632}
2025-06-20 10:30:32,473 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: b55015dd5e71e056)
2025-06-20 10:30:32,588 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:30:32,588 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:30:32,588 - src.network.client - ERROR - 处理握手响应失败: Ciphertext length must be equal to key size.
2025-06-20 10:30:34,519 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x92 in position 5: invalid start byte
2025-06-20 10:30:38,537 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x93 in position 9: invalid start byte
2025-06-20 10:30:43,552 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa2 in position 7: invalid start byte
2025-06-20 10:30:57,512 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb6 in position 5: invalid start byte
2025-06-20 10:30:57,665 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:30:57,665 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:31:03,473 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:31:03,473 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '707b793495aeff84', 'timestamp': 1750386663}
2025-06-20 10:31:03,473 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '707b793495aeff84', 'timestamp': 1750386663}
2025-06-20 10:31:03,473 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 707b793495aeff84)
2025-06-20 10:31:03,551 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa3 in position 5: invalid start byte
2025-06-20 10:31:04,575 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb2 in position 5: invalid start byte
2025-06-20 10:31:19,594 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xee in position 5: invalid continuation byte
2025-06-20 10:31:21,517 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xaa in position 5: invalid start byte
2025-06-20 10:31:27,670 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:31:27,670 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:31:33,475 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:31:33,475 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '4232ad1ba8ccc98a', 'timestamp': 1750386693}
2025-06-20 10:31:33,475 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '4232ad1ba8ccc98a', 'timestamp': 1750386693}
2025-06-20 10:31:33,475 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 4232ad1ba8ccc98a)
2025-06-20 10:31:40,552 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa9 in position 5: invalid start byte
2025-06-20 10:31:43,524 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xeb in position 7: invalid continuation byte
2025-06-20 10:31:54,604 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc4 in position 5: invalid continuation byte
2025-06-20 10:31:57,669 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:31:57,669 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:32:03,483 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:32:03,483 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '0687c1239a87d9d0', 'timestamp': 1750386723}
2025-06-20 10:32:03,484 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '0687c1239a87d9d0', 'timestamp': 1750386723}
2025-06-20 10:32:03,484 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 0687c1239a87d9d0)
2025-06-20 10:32:16,534 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb1 in position 5: invalid start byte
2025-06-20 10:32:19,571 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfe in position 9: invalid start byte
2025-06-20 10:32:26,604 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x85 in position 7: invalid start byte
2025-06-20 10:32:27,672 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:32:27,674 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:32:33,484 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:32:33,484 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'c7b00a85be3796d1', 'timestamp': 1750386753}
2025-06-20 10:32:33,484 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'c7b00a85be3796d1', 'timestamp': 1750386753}
2025-06-20 10:32:33,484 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: c7b00a85be3796d1)
2025-06-20 10:32:43,539 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 5-6: invalid continuation byte
2025-06-20 10:32:47,606 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xcc in position 7: invalid continuation byte
2025-06-20 10:32:52,578 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd8 in position 6: invalid continuation byte
2025-06-20 10:32:57,658 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:32:57,658 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:33:02,606 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 5-6: invalid continuation byte
2025-06-20 10:33:03,485 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:33:03,485 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '3a93a0eda4419703', 'timestamp': 1750386783}
2025-06-20 10:33:03,485 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '3a93a0eda4419703', 'timestamp': 1750386783}
2025-06-20 10:33:03,485 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 3a93a0eda4419703)
2025-06-20 10:33:07,557 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xed in position 6: invalid continuation byte
2025-06-20 10:33:15,579 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x88 in position 9: invalid start byte
2025-06-20 10:33:18,615 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd2 in position 8: invalid continuation byte
2025-06-20 10:33:20,555 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xfc in position 8: invalid start byte
2025-06-20 10:33:27,670 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:33:27,670 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:33:31,584 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xad in position 7: invalid start byte
2025-06-20 10:33:33,487 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:33:33,487 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '97647737882a1bb0', 'timestamp': 1750386813}
2025-06-20 10:33:33,487 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '97647737882a1bb0', 'timestamp': 1750386813}
2025-06-20 10:33:33,487 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 97647737882a1bb0)
2025-06-20 10:33:46,574 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xa8 in position 5: invalid start byte
2025-06-20 10:33:48,622 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9a in position 7: invalid start byte
2025-06-20 10:33:57,667 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:33:57,667 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:34:00,592 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xbf in position 13: invalid start byte
2025-06-20 10:34:01,581 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xe1 in position 7: invalid continuation byte
2025-06-20 10:34:03,492 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:34:03,492 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '5484b5b04683c63b', 'timestamp': 1750386843}
2025-06-20 10:34:03,492 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '5484b5b04683c63b', 'timestamp': 1750386843}
2025-06-20 10:34:03,492 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 5484b5b04683c63b)
2025-06-20 10:34:14,600 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x82 in position 5: invalid start byte
2025-06-20 10:34:21,582 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x88 in position 6: invalid start byte
2025-06-20 10:34:21,632 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9f in position 7: invalid start byte
2025-06-20 10:34:27,613 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x84 in position 5: invalid start byte
2025-06-20 10:34:27,668 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:34:27,668 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:34:32,520 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-20 10:34:32,520 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-20 10:34:32,520 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 20, 传输字节: 5336
2025-06-20 10:34:33,497 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:34:33,497 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'a088b7dd1dc53cde', 'timestamp': 1750386873}
2025-06-20 10:34:33,497 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'a088b7dd1dc53cde', 'timestamp': 1750386873}
2025-06-20 10:34:33,497 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: a088b7dd1dc53cde)
2025-06-20 10:34:47,610 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xf0 in position 7: invalid continuation byte
2025-06-20 10:34:53,588 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd0 in position 5: invalid continuation byte
2025-06-20 10:34:55,640 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x9a in position 7: invalid start byte
2025-06-20 10:34:57,619 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb7 in position 7: invalid start byte
2025-06-20 10:34:57,667 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:34:57,667 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:35:03,504 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:35:03,504 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'eb821102cd4fe5ba', 'timestamp': 1750386903}
2025-06-20 10:35:03,504 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'eb821102cd4fe5ba', 'timestamp': 1750386903}
2025-06-20 10:35:03,506 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: eb821102cd4fe5ba)
2025-06-20 10:35:06,587 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb8 in position 6: invalid start byte
2025-06-20 10:35:25,623 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd0 in position 5: invalid continuation byte
2025-06-20 10:35:26,647 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0x92 in position 5: invalid start byte
2025-06-20 10:35:27,663 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:35:27,663 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:35:32,601 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-20 10:35:32,601 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:35:32,601 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-20 10:35:32,601 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:35:32,601 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:35:32,601 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-20 10:35:32,601 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:35:32,601 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-20 10:35:32,601 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:35:32,601 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:35:33,516 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:35:33,516 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '31bab184b40de192', 'timestamp': 1750386933}
2025-06-20 10:35:33,516 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '31bab184b40de192', 'timestamp': 1750386933}
2025-06-20 10:35:33,516 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 31bab184b40de192)
2025-06-20 10:35:44,601 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xd0 in position 6: invalid continuation byte
2025-06-20 10:35:50,617 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xba in position 6: invalid start byte
2025-06-20 10:35:57,671 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:35:57,674 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_58a52e5e41090f7e -> bbb
2025-06-20 10:36:01,661 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xc7 in position 5: invalid continuation byte
2025-06-20 10:36:03,524 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_36112f92acf7836d
2025-06-20 10:36:03,524 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '03a39f4f69b4c48a', 'timestamp': 1750386963}
2025-06-20 10:36:03,524 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '03a39f4f69b4c48a', 'timestamp': 1750386963}
2025-06-20 10:36:03,524 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 03a39f4f69b4c48a)
2025-06-20 10:36:11,620 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode bytes in position 5-6: invalid continuation byte
2025-06-20 10:36:21,611 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xb3 in position 5: invalid start byte
2025-06-20 10:36:26,634 - src.network.client - ERROR - 处理消息失败: 'utf-8' codec can't decode byte 0xcb in position 8: invalid continuation byte
2025-06-20 10:36:27,075 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_36112f92acf7836d
2025-06-20 10:36:27,075 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-20 10:36:27,075 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:27,075 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_77da0afa995b14b7
2025-06-20 10:36:27,075 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:27,075 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_01fa3f1268518f24
2025-06-20 10:36:27,075 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:27,679 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_58a52e5e41090f7e
2025-06-20 10:36:27,679 - RelayServer-relay_8011 - WARNING - 目标用户未找到: bbb
2025-06-20 10:36:28,061 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_58a52e5e41090f7e
2025-06-20 10:36:28,062 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-20 10:36:28,062 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:28,062 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_fb9b7c825e0c34c8
2025-06-20 10:36:28,062 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:28,062 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_a4098ac9545b079c
2025-06-20 10:36:28,062 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:30,674 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:36:32,613 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:32,615 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:32,615 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:36:32,615 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:32,615 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:36:32,615 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:36:34,616 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:36:43,638 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:36:53,640 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:37:08,676 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:37:12,629 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:37:25,633 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:37:32,633 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:37:32,634 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:37:32,634 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:37:32,634 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:37:32,634 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:37:32,634 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:37:32,634 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:37:45,684 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:37:50,644 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:01,698 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:05,646 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:11,698 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:18,641 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:30,658 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:32,643 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:38:32,643 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:38:32,643 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:38:32,643 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:38:32,643 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:38:32,643 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:38:32,710 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:40,658 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:38:57,649 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:04,658 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:05,726 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:19,651 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:20,654 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:26,737 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:32,525 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-20 10:39:32,525 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-20 10:39:32,525 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 2, 总连接: 2, 中继消息: 28, 传输字节: 7104
2025-06-20 10:39:32,640 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:39:32,640 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:39:32,640 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:39:32,640 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:39:32,640 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:39:32,640 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:39:52,671 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:52,671 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:39:53,746 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:40:22,673 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:40:26,754 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:40:29,687 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:40:32,647 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:40:32,647 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:40:32,647 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:40:32,647 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:40:32,647 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:40:32,647 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:40:43,664 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:40:55,753 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:40:57,689 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:41:03,696 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:41:07,681 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:41:17,754 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:41:32,651 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:41:32,651 - RelayServer-relay_8011 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:41:32,651 - RelayServer-relay_8011 - INFO - 清理了 2 个过期连接
2025-06-20 10:41:32,651 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:41:32,651 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:41:32,651 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:41:32,651 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:41:32,651 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'ServerConnection' object has no attribute 'closed'
2025-06-20 10:41:32,651 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:41:40,687 - RelayServer-relay_8012 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:41:41,705 - RelayServer-relay_8011 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:41:50,773 - RelayServer-relay_8013 - ERROR - 生成虚假流量时出错: no close frame received or sent
2025-06-20 10:42:12,597 - RelayServer-relay_8011 - INFO - 启动中继服务器 relay_8011 on localhost:8011
2025-06-20 10:42:12,607 - RelayServer-relay_8012 - INFO - 启动中继服务器 relay_8012 on localhost:8012
2025-06-20 10:42:12,607 - RelayServer-relay_8013 - INFO - 启动中继服务器 relay_8013 on localhost:8013
2025-06-20 10:42:12,619 - websockets.server - INFO - server listening on 127.0.0.1:8012
2025-06-20 10:42:12,619 - websockets.server - INFO - server listening on [::1]:8012
2025-06-20 10:42:12,619 - RelayServer-relay_8012 - INFO - ✓ 中继服务器已启动: ws://localhost:8012
2025-06-20 10:42:12,619 - websockets.server - INFO - server listening on [::1]:8011
2025-06-20 10:42:12,619 - websockets.server - INFO - server listening on 127.0.0.1:8011
2025-06-20 10:42:12,619 - RelayServer-relay_8011 - INFO - ✓ 中继服务器已启动: ws://localhost:8011
2025-06-20 10:42:12,619 - websockets.server - INFO - server listening on 127.0.0.1:8013
2025-06-20 10:42:12,619 - websockets.server - INFO - server listening on [::1]:8013
2025-06-20 10:42:12,619 - RelayServer-relay_8013 - INFO - ✓ 中继服务器已启动: ws://localhost:8013
2025-06-20 10:42:33,376 - websockets.server - INFO - connection open
2025-06-20 10:42:33,376 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_0f972d297a971159 from ('127.0.0.1', 51654)
2025-06-20 10:42:33,376 - websockets.server - INFO - connection open
2025-06-20 10:42:33,376 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_d9f755399a89d234 from ('127.0.0.1', 51655)
2025-06-20 10:42:33,383 - websockets.server - INFO - connection open
2025-06-20 10:42:33,386 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_aba92df66f26892f from ('127.0.0.1', 51656)
2025-06-20 10:42:33,386 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_0f972d297a971159
2025-06-20 10:42:33,386 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_0f972d297a971159
2025-06-20 10:42:33,386 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_d9f755399a89d234
2025-06-20 10:42:33,386 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_d9f755399a89d234
2025-06-20 10:42:33,386 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_aba92df66f26892f
2025-06-20 10:42:33,389 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_aba92df66f26892f
2025-06-20 10:42:57,215 - websockets.server - INFO - connection open
2025-06-20 10:42:57,215 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_b4462dd182830f9b from ('127.0.0.1', 51691)
2025-06-20 10:42:57,221 - websockets.server - INFO - connection open
2025-06-20 10:42:57,221 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_43fe0c23de470d5f from ('127.0.0.1', 51692)
2025-06-20 10:42:57,225 - websockets.server - INFO - connection open
2025-06-20 10:42:57,226 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_698b2b87d83f235c from ('127.0.0.1', 51693)
2025-06-20 10:42:57,226 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_b4462dd182830f9b
2025-06-20 10:42:57,226 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_b4462dd182830f9b
2025-06-20 10:42:57,227 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_43fe0c23de470d5f
2025-06-20 10:42:57,227 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_43fe0c23de470d5f
2025-06-20 10:42:57,227 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_698b2b87d83f235c
2025-06-20 10:42:57,227 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_698b2b87d83f235c
2025-06-20 10:43:11,216 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_b4462dd182830f9b
2025-06-20 10:43:11,216 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_b4462dd182830f9b -> aaa
2025-06-20 10:43:11,226 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:43:11,226 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '0dca60a0c066d149', 'timestamp': 1750387391}
2025-06-20 10:43:11,226 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '0dca60a0c066d149', 'timestamp': 1750387391}
2025-06-20 10:43:11,229 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 0dca60a0c066d149)
2025-06-20 10:43:11,335 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:43:11,335 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:43:11,335 - src.network.client - ERROR - 处理握手响应失败: Ciphertext length must be equal to key size.
2025-06-20 10:43:33,412 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:43:33,412 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:43:42,247 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:43:42,249 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'da1d36e0db0eb3f9', 'timestamp': 1750387422}
2025-06-20 10:43:42,249 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'da1d36e0db0eb3f9', 'timestamp': 1750387422}
2025-06-20 10:43:42,249 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: da1d36e0db0eb3f9)
2025-06-20 10:44:03,397 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:44:03,397 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:44:12,249 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:44:12,249 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '5e6ed535858f4433', 'timestamp': 1750387452}
2025-06-20 10:44:12,249 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '5e6ed535858f4433', 'timestamp': 1750387452}
2025-06-20 10:44:12,249 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 5e6ed535858f4433)
2025-06-20 10:44:33,398 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:44:33,400 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:44:42,257 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:44:42,257 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '23f62c44f6a7d617', 'timestamp': 1750387482}
2025-06-20 10:44:42,257 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '23f62c44f6a7d617', 'timestamp': 1750387482}
2025-06-20 10:44:42,257 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 23f62c44f6a7d617)
2025-06-20 10:45:03,416 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:45:03,417 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:45:12,264 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:45:12,264 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '511ef7e9ab9eb0d9', 'timestamp': 1750387512}
2025-06-20 10:45:12,264 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '511ef7e9ab9eb0d9', 'timestamp': 1750387512}
2025-06-20 10:45:12,264 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 511ef7e9ab9eb0d9)
2025-06-20 10:45:33,425 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:45:33,425 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:45:42,282 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:45:42,282 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'e817c7ce9dd4e4b3', 'timestamp': 1750387542}
2025-06-20 10:45:42,282 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'e817c7ce9dd4e4b3', 'timestamp': 1750387542}
2025-06-20 10:45:42,282 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: e817c7ce9dd4e4b3)
2025-06-20 10:46:03,421 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:46:03,422 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:46:12,294 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:46:12,294 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'fb477204b1d163a1', 'timestamp': 1750387572}
2025-06-20 10:46:12,294 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'fb477204b1d163a1', 'timestamp': 1750387572}
2025-06-20 10:46:12,295 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: fb477204b1d163a1)
2025-06-20 10:46:33,405 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:46:33,406 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:46:42,292 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:46:42,292 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '78e3f7e01fbd54f1', 'timestamp': 1750387602}
2025-06-20 10:46:42,292 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '78e3f7e01fbd54f1', 'timestamp': 1750387602}
2025-06-20 10:46:42,292 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 78e3f7e01fbd54f1)
2025-06-20 10:47:03,411 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_0f972d297a971159
2025-06-20 10:47:03,411 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_0f972d297a971159 -> bbb
2025-06-20 10:47:12,295 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_b4462dd182830f9b
2025-06-20 10:47:12,295 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'd80b0d5d6ef63520', 'timestamp': 1750387632}
2025-06-20 10:47:12,295 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'd80b0d5d6ef63520', 'timestamp': 1750387632}
2025-06-20 10:47:12,295 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: d80b0d5d6ef63520)
2025-06-20 10:47:12,632 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-20 10:47:12,632 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 2, 传输字节: 126
2025-06-20 10:47:12,632 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 300s, 活跃连接: 2, 总连接: 2, 中继消息: 21, 传输字节: 5441
2025-06-20 10:47:30,210 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_43fe0c23de470d5f
2025-06-20 10:47:30,218 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-20 10:47:30,218 - RelayServer-relay_8012 - INFO - 客户端已断开: client_43fe0c23de470d5f - Unknown
2025-06-20 10:47:30,218 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_b4462dd182830f9b
2025-06-20 10:47:30,218 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-20 10:47:30,218 - RelayServer-relay_8011 - INFO - 客户端已断开: client_b4462dd182830f9b - Unknown
2025-06-20 10:47:30,218 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_698b2b87d83f235c
2025-06-20 10:47:30,218 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-20 10:47:30,221 - RelayServer-relay_8013 - INFO - 客户端已断开: client_698b2b87d83f235c - Unknown
2025-06-20 10:47:31,065 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_0f972d297a971159
2025-06-20 10:47:31,065 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-20 10:47:31,065 - RelayServer-relay_8011 - INFO - 客户端已断开: client_0f972d297a971159 - Unknown
2025-06-20 10:47:31,065 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_d9f755399a89d234
2025-06-20 10:47:31,065 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-20 10:47:31,065 - RelayServer-relay_8012 - INFO - 客户端已断开: client_d9f755399a89d234 - Unknown
2025-06-20 10:47:31,065 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_aba92df66f26892f
2025-06-20 10:47:31,065 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-20 10:47:31,065 - RelayServer-relay_8013 - INFO - 客户端已断开: client_aba92df66f26892f - Unknown
2025-06-20 10:49:37,338 - websockets.server - INFO - connection open
2025-06-20 10:49:37,340 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_3c3fe9f12bb750ec from ('127.0.0.1', 52178)
2025-06-20 10:49:37,340 - websockets.server - INFO - connection open
2025-06-20 10:49:37,340 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_d8e9a0eb26282860 from ('127.0.0.1', 52179)
2025-06-20 10:49:37,345 - websockets.server - INFO - connection open
2025-06-20 10:49:37,345 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_6f6f9745bdf77ab1 from ('127.0.0.1', 52180)
2025-06-20 10:49:37,345 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_3c3fe9f12bb750ec
2025-06-20 10:49:37,347 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_3c3fe9f12bb750ec
2025-06-20 10:49:37,347 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_d8e9a0eb26282860
2025-06-20 10:49:37,347 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_d8e9a0eb26282860
2025-06-20 10:49:37,347 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_6f6f9745bdf77ab1
2025-06-20 10:49:37,347 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_6f6f9745bdf77ab1
2025-06-20 10:49:59,660 - websockets.server - INFO - connection open
2025-06-20 10:49:59,660 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_fbd31f5cfb745be9 from ('127.0.0.1', 52198)
2025-06-20 10:49:59,660 - websockets.server - INFO - connection open
2025-06-20 10:49:59,669 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_e80d5b6eae9ed1e6 from ('127.0.0.1', 52199)
2025-06-20 10:49:59,670 - websockets.server - INFO - connection open
2025-06-20 10:49:59,670 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_cfd82e56c85580f2 from ('127.0.0.1', 52200)
2025-06-20 10:49:59,670 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_fbd31f5cfb745be9
2025-06-20 10:49:59,670 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_fbd31f5cfb745be9
2025-06-20 10:49:59,670 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_e80d5b6eae9ed1e6
2025-06-20 10:49:59,675 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_e80d5b6eae9ed1e6
2025-06-20 10:49:59,675 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_cfd82e56c85580f2
2025-06-20 10:49:59,675 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_cfd82e56c85580f2
2025-06-20 10:50:10,649 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_fbd31f5cfb745be9
2025-06-20 10:50:10,649 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_fbd31f5cfb745be9 -> aaa
2025-06-20 10:50:10,665 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_fbd31f5cfb745be9
2025-06-20 10:50:10,667 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '458a58560a8c8e39', 'timestamp': 1750387810}
2025-06-20 10:50:10,667 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '458a58560a8c8e39', 'timestamp': 1750387810}
2025-06-20 10:50:10,667 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 458a58560a8c8e39)
2025-06-20 10:50:10,776 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_3c3fe9f12bb750ec
2025-06-20 10:50:10,776 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_3c3fe9f12bb750ec -> bbb
2025-06-20 10:50:10,776 - src.network.client - ERROR - 处理握手响应失败: 密钥交换完成失败: 密文长度 (32) 与密钥大小 (256) 不匹配
2025-06-20 10:50:37,351 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_3c3fe9f12bb750ec
2025-06-20 10:50:37,351 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_3c3fe9f12bb750ec -> bbb
2025-06-20 10:50:41,683 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_fbd31f5cfb745be9
2025-06-20 10:50:41,683 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '874def4dff057dec', 'timestamp': 1750387841}
2025-06-20 10:50:41,683 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '874def4dff057dec', 'timestamp': 1750387841}
2025-06-20 10:50:41,683 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 874def4dff057dec)
2025-06-20 10:51:07,370 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_3c3fe9f12bb750ec
2025-06-20 10:51:07,370 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_3c3fe9f12bb750ec -> bbb
2025-06-20 10:51:11,685 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_fbd31f5cfb745be9
2025-06-20 10:51:11,685 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '928d36ecc49b21fc', 'timestamp': 1750387871}
2025-06-20 10:51:11,685 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '928d36ecc49b21fc', 'timestamp': 1750387871}
2025-06-20 10:51:11,685 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 928d36ecc49b21fc)
2025-06-20 10:51:31,509 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_3c3fe9f12bb750ec
2025-06-20 10:51:31,509 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-20 10:51:31,509 - RelayServer-relay_8011 - INFO - 客户端已断开: client_3c3fe9f12bb750ec - Unknown
2025-06-20 10:51:31,509 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_d8e9a0eb26282860
2025-06-20 10:51:31,509 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-20 10:51:31,509 - RelayServer-relay_8012 - INFO - 客户端已断开: client_d8e9a0eb26282860 - Unknown
2025-06-20 10:51:31,509 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_6f6f9745bdf77ab1
2025-06-20 10:51:31,509 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-20 10:51:31,509 - RelayServer-relay_8013 - INFO - 客户端已断开: client_6f6f9745bdf77ab1 - Unknown
2025-06-20 10:51:32,379 - RelayServer-relay_8013 - INFO - 客户端断开连接: client_cfd82e56c85580f2
2025-06-20 10:51:32,380 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-20 10:51:32,380 - RelayServer-relay_8013 - INFO - 客户端已断开: client_cfd82e56c85580f2 - Unknown
2025-06-20 10:51:32,380 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_fbd31f5cfb745be9
2025-06-20 10:51:32,380 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-20 10:51:32,380 - RelayServer-relay_8011 - INFO - 客户端已断开: client_fbd31f5cfb745be9 - Unknown
2025-06-20 10:51:32,380 - RelayServer-relay_8012 - INFO - 客户端断开连接: client_e80d5b6eae9ed1e6
2025-06-20 10:51:32,380 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-20 10:51:32,380 - RelayServer-relay_8012 - INFO - 客户端已断开: client_e80d5b6eae9ed1e6 - Unknown
2025-06-20 10:52:12,643 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 252
2025-06-20 10:52:12,643 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 4, 中继消息: 30, 传输字节: 8230
2025-06-20 10:52:12,644 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 600s, 活跃连接: 0, 总连接: 4, 中继消息: 4, 传输字节: 252
2025-06-20 10:52:45,206 - websockets.server - INFO - connection open
2025-06-20 10:52:45,206 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_91d80ceae91cc0fd from ('127.0.0.1', 52354)
2025-06-20 10:52:45,213 - websockets.server - INFO - connection open
2025-06-20 10:52:45,213 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_620f825f2801ba79 from ('127.0.0.1', 52355)
2025-06-20 10:52:45,221 - websockets.server - INFO - connection open
2025-06-20 10:52:45,221 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_3de1694d3ed336b6 from ('127.0.0.1', 52356)
2025-06-20 10:52:45,223 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_91d80ceae91cc0fd
2025-06-20 10:52:45,223 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_91d80ceae91cc0fd
2025-06-20 10:52:45,225 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_620f825f2801ba79
2025-06-20 10:52:45,225 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_620f825f2801ba79
2025-06-20 10:52:45,225 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_3de1694d3ed336b6
2025-06-20 10:52:45,225 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_3de1694d3ed336b6
2025-06-20 10:53:11,126 - websockets.server - INFO - connection open
2025-06-20 10:53:11,126 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_997f055ddd7ef128 from ('127.0.0.1', 52380)
2025-06-20 10:53:11,131 - websockets.server - INFO - connection open
2025-06-20 10:53:11,131 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_a4051cc2b8af4062 from ('127.0.0.1', 52381)
2025-06-20 10:53:11,132 - websockets.server - INFO - connection open
2025-06-20 10:53:11,132 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_3b7c79abc7600bd6 from ('127.0.0.1', 52382)
2025-06-20 10:53:11,132 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_997f055ddd7ef128
2025-06-20 10:53:11,137 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_997f055ddd7ef128
2025-06-20 10:53:11,137 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_a4051cc2b8af4062
2025-06-20 10:53:11,138 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_a4051cc2b8af4062
2025-06-20 10:53:11,138 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_3b7c79abc7600bd6
2025-06-20 10:53:11,138 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_3b7c79abc7600bd6
2025-06-20 10:53:21,590 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:53:21,590 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:53:21,596 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:53:21,596 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '696622232701d210', 'timestamp': 1750388001}
2025-06-20 10:53:21,596 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '696622232701d210', 'timestamp': 1750388001}
2025-06-20 10:53:21,596 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 696622232701d210)
2025-06-20 10:53:21,667 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:53:21,667 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:53:21,667 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:53:21,667 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:53:41,137 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:53:41,140 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:53:45,227 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:53:45,228 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:53:52,602 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:53:52,602 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '3fad66fb6fb5381b', 'timestamp': 1750388032}
2025-06-20 10:53:52,602 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '3fad66fb6fb5381b', 'timestamp': 1750388032}
2025-06-20 10:53:52,602 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 3fad66fb6fb5381b)
2025-06-20 10:54:11,129 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:54:11,129 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:54:15,235 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:54:15,235 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:54:22,603 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:54:22,603 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '757744639162b3d5', 'timestamp': 1750388062}
2025-06-20 10:54:22,603 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '757744639162b3d5', 'timestamp': 1750388062}
2025-06-20 10:54:22,603 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 757744639162b3d5)
2025-06-20 10:54:41,144 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:54:41,144 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:54:45,238 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:54:45,238 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:54:52,614 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:54:52,614 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '563b1bc57a77e2ec', 'timestamp': 1750388092}
2025-06-20 10:54:52,614 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '563b1bc57a77e2ec', 'timestamp': 1750388092}
2025-06-20 10:54:52,614 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 563b1bc57a77e2ec)
2025-06-20 10:55:11,153 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:55:11,153 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:55:15,228 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:55:15,229 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:55:22,628 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:55:22,628 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '1f64d0e6d860f160', 'timestamp': 1750388122}
2025-06-20 10:55:22,628 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '1f64d0e6d860f160', 'timestamp': 1750388122}
2025-06-20 10:55:22,628 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 1f64d0e6d860f160)
2025-06-20 10:55:41,154 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:55:41,154 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:55:45,237 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:55:45,237 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:55:52,639 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:55:52,639 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '177e267eaeea5206', 'timestamp': 1750388152}
2025-06-20 10:55:52,639 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '177e267eaeea5206', 'timestamp': 1750388152}
2025-06-20 10:55:52,639 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 177e267eaeea5206)
2025-06-20 10:56:11,165 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:56:11,166 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:56:15,231 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:56:15,231 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:56:22,641 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:56:22,641 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '6789fa3c0cd30171', 'timestamp': 1750388182}
2025-06-20 10:56:22,641 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '6789fa3c0cd30171', 'timestamp': 1750388182}
2025-06-20 10:56:22,641 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 6789fa3c0cd30171)
2025-06-20 10:56:41,172 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:56:41,172 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:56:45,249 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:56:45,249 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:56:52,641 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:56:52,645 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '00b70e62bb76cd23', 'timestamp': 1750388212}
2025-06-20 10:56:52,645 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '00b70e62bb76cd23', 'timestamp': 1750388212}
2025-06-20 10:56:52,645 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 00b70e62bb76cd23)
2025-06-20 10:57:11,163 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:57:11,164 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:57:12,643 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 6, 中继消息: 6, 传输字节: 378
2025-06-20 10:57:12,643 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 6, 中继消息: 6, 传输字节: 378
2025-06-20 10:57:12,643 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 900s, 活跃连接: 2, 总连接: 6, 中继消息: 58, 传输字节: 16829
2025-06-20 10:57:15,247 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:57:15,247 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:57:22,653 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:57:22,653 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '73cd3acadcc0d915', 'timestamp': 1750388242}
2025-06-20 10:57:22,653 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '73cd3acadcc0d915', 'timestamp': 1750388242}
2025-06-20 10:57:22,653 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 73cd3acadcc0d915)
2025-06-20 10:57:41,171 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:57:41,171 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:57:45,253 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:57:45,255 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:57:52,657 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:57:52,657 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '366ce83ec062badf', 'timestamp': 1750388272}
2025-06-20 10:57:52,657 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '366ce83ec062badf', 'timestamp': 1750388272}
2025-06-20 10:57:52,657 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 366ce83ec062badf)
2025-06-20 10:58:11,173 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:58:11,173 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:58:12,706 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-20 10:58:12,706 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-20 10:58:12,706 - RelayServer-relay_8012 - INFO - 客户端已断开: client_620f825f2801ba79 - Unknown
2025-06-20 10:58:12,706 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'client_620f825f2801ba79'
2025-06-20 10:58:12,706 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-20 10:58:12,706 - RelayServer-relay_8013 - INFO - 客户端已断开: client_3de1694d3ed336b6 - Unknown
2025-06-20 10:58:12,706 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'client_3de1694d3ed336b6'
2025-06-20 10:58:12,706 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-20 10:58:12,713 - RelayServer-relay_8012 - INFO - 客户端已断开: client_a4051cc2b8af4062 - Unknown
2025-06-20 10:58:12,713 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'client_a4051cc2b8af4062'
2025-06-20 10:58:12,714 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 10:58:12,714 - RelayServer-relay_8013 - INFO - 客户端已断开: client_3b7c79abc7600bd6 - Unknown
2025-06-20 10:58:12,714 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'client_3b7c79abc7600bd6'
2025-06-20 10:58:12,714 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 10:58:15,254 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:58:15,254 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:58:22,666 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:58:22,666 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'd1bd50635593d88e', 'timestamp': 1750388302}
2025-06-20 10:58:22,666 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'd1bd50635593d88e', 'timestamp': 1750388302}
2025-06-20 10:58:22,666 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: d1bd50635593d88e)
2025-06-20 10:58:41,169 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:58:41,169 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:58:45,256 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:58:45,256 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:58:52,675 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:58:52,675 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'e3192bac161c5990', 'timestamp': 1750388332}
2025-06-20 10:58:52,675 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'e3192bac161c5990', 'timestamp': 1750388332}
2025-06-20 10:58:52,675 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: e3192bac161c5990)
2025-06-20 10:59:11,177 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:59:11,177 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:59:15,259 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:59:15,259 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:59:22,680 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:59:22,680 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '74fb2a3b1dee0222', 'timestamp': 1750388362}
2025-06-20 10:59:22,680 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '74fb2a3b1dee0222', 'timestamp': 1750388362}
2025-06-20 10:59:22,680 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 74fb2a3b1dee0222)
2025-06-20 10:59:41,186 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 10:59:41,186 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 10:59:45,258 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 10:59:45,258 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 10:59:52,696 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 10:59:52,696 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'df6314c26618a664', 'timestamp': 1750388392}
2025-06-20 10:59:52,696 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'df6314c26618a664', 'timestamp': 1750388392}
2025-06-20 10:59:52,696 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: df6314c26618a664)
2025-06-20 11:00:11,197 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 11:00:11,197 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 11:00:15,270 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 11:00:15,270 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 11:00:22,702 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 11:00:22,702 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '08927e69d753d159', 'timestamp': 1750388422}
2025-06-20 11:00:22,702 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '08927e69d753d159', 'timestamp': 1750388422}
2025-06-20 11:00:22,702 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 08927e69d753d159)
2025-06-20 11:00:41,213 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_997f055ddd7ef128
2025-06-20 11:00:41,213 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_997f055ddd7ef128 -> aaa
2025-06-20 11:00:45,271 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_91d80ceae91cc0fd
2025-06-20 11:00:45,271 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_91d80ceae91cc0fd -> bbb
2025-06-20 11:00:52,709 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_997f055ddd7ef128
2025-06-20 11:00:52,709 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '0eb9c1e5b48f940f', 'timestamp': 1750388452}
2025-06-20 11:00:52,709 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '0eb9c1e5b48f940f', 'timestamp': 1750388452}
2025-06-20 11:00:52,709 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 0eb9c1e5b48f940f)
2025-06-20 11:01:00,891 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_91d80ceae91cc0fd
2025-06-20 11:01:00,891 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-20 11:01:00,891 - RelayServer-relay_8011 - INFO - 客户端已断开: client_91d80ceae91cc0fd - Unknown
2025-06-20 11:01:01,903 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_997f055ddd7ef128
2025-06-20 11:01:01,903 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-20 11:01:01,903 - RelayServer-relay_8011 - INFO - 客户端已断开: client_997f055ddd7ef128 - Unknown
2025-06-20 11:01:18,408 - websockets.server - INFO - connection open
2025-06-20 11:01:18,408 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_8716fc4754ed06dc from ('127.0.0.1', 52945)
2025-06-20 11:01:18,410 - websockets.server - INFO - connection open
2025-06-20 11:01:18,410 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_24d3a4542bb1139b from ('127.0.0.1', 52946)
2025-06-20 11:01:18,417 - websockets.server - INFO - connection open
2025-06-20 11:01:18,417 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_4a36cf892cc38643 from ('127.0.0.1', 52947)
2025-06-20 11:01:18,419 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_8716fc4754ed06dc
2025-06-20 11:01:18,420 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_8716fc4754ed06dc
2025-06-20 11:01:18,420 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_24d3a4542bb1139b
2025-06-20 11:01:18,420 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_24d3a4542bb1139b
2025-06-20 11:01:18,420 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_4a36cf892cc38643
2025-06-20 11:01:18,420 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_4a36cf892cc38643
2025-06-20 11:01:40,103 - websockets.server - INFO - connection open
2025-06-20 11:01:40,103 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_efa0f415c162b0b2 from ('127.0.0.1', 52972)
2025-06-20 11:01:40,106 - websockets.server - INFO - connection open
2025-06-20 11:01:40,106 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_4fc9025f68708a74 from ('127.0.0.1', 52973)
2025-06-20 11:01:40,113 - websockets.server - INFO - connection open
2025-06-20 11:01:40,114 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_dbf77fb6ad95e798 from ('127.0.0.1', 52974)
2025-06-20 11:01:40,114 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_efa0f415c162b0b2
2025-06-20 11:01:40,116 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_efa0f415c162b0b2
2025-06-20 11:01:40,116 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_4fc9025f68708a74
2025-06-20 11:01:40,116 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_4fc9025f68708a74
2025-06-20 11:01:40,117 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_dbf77fb6ad95e798
2025-06-20 11:01:40,117 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: bbb (bbb) -> client_dbf77fb6ad95e798
2025-06-20 11:01:51,270 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:01:51,270 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:01:51,281 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:01:51,281 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '61498e14a01cd153', 'timestamp': 1750388511}
2025-06-20 11:01:51,281 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '61498e14a01cd153', 'timestamp': 1750388511}
2025-06-20 11:01:51,281 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 61498e14a01cd153)
2025-06-20 11:01:51,346 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:01:51,346 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:01:51,346 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:01:51,346 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:02:10,116 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:02:10,116 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:02:12,648 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 2, 总连接: 8, 中继消息: 8, 传输字节: 504
2025-06-20 11:02:12,648 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 2, 总连接: 8, 中继消息: 8, 传输字节: 504
2025-06-20 11:02:12,648 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 1200s, 活跃连接: 2, 总连接: 8, 中继消息: 88, 传输字节: 25870
2025-06-20 11:02:14,429 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:02:14,429 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:02:18,422 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:02:18,423 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:02:22,293 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:02:22,293 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'b04412063ec4fdce', 'timestamp': 1750388542}
2025-06-20 11:02:22,293 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'b04412063ec4fdce', 'timestamp': 1750388542}
2025-06-20 11:02:22,293 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: b04412063ec4fdce)
2025-06-20 11:02:40,124 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:02:40,124 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:02:40,178 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_8716fc4754ed06dc
2025-06-20 11:02:40,178 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'bbb', 'query_id': '51966df81e3fd522', 'timestamp': 1750388560}
2025-06-20 11:02:40,178 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'bbb', 'query_id': '51966df81e3fd522', 'timestamp': 1750388560}
2025-06-20 11:02:40,178 - RelayServer-relay_8011 - INFO - ✓ 用户查询: bbb -> 在线 (查询ID: 51966df81e3fd522)
2025-06-20 11:02:48,420 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:02:48,420 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:02:52,310 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:02:52,310 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '23c8786c947c84f1', 'timestamp': 1750388572}
2025-06-20 11:02:52,310 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '23c8786c947c84f1', 'timestamp': 1750388572}
2025-06-20 11:02:52,314 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 23c8786c947c84f1)
2025-06-20 11:03:10,124 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:03:10,124 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:03:18,416 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:03:18,416 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:03:22,308 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:03:22,308 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'acbcb0b9701b6147', 'timestamp': 1750388602}
2025-06-20 11:03:22,308 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'acbcb0b9701b6147', 'timestamp': 1750388602}
2025-06-20 11:03:22,308 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: acbcb0b9701b6147)
2025-06-20 11:03:40,139 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:03:40,139 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:03:48,413 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:03:48,413 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:03:52,312 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:03:52,313 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '1a77473df5d6721e', 'timestamp': 1750388632}
2025-06-20 11:03:52,313 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '1a77473df5d6721e', 'timestamp': 1750388632}
2025-06-20 11:03:52,313 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 1a77473df5d6721e)
2025-06-20 11:04:10,157 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:04:10,157 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:04:18,423 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:04:18,425 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:04:22,322 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:04:22,322 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'd6548c928cb65cae', 'timestamp': 1750388662}
2025-06-20 11:04:22,322 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'd6548c928cb65cae', 'timestamp': 1750388662}
2025-06-20 11:04:22,322 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: d6548c928cb65cae)
2025-06-20 11:04:40,157 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:04:40,157 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:04:48,429 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:04:48,429 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:04:52,331 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:04:52,331 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '50b80a96981d0c91', 'timestamp': 1750388692}
2025-06-20 11:04:52,331 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '50b80a96981d0c91', 'timestamp': 1750388692}
2025-06-20 11:04:52,331 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 50b80a96981d0c91)
2025-06-20 11:05:10,164 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:05:10,164 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:05:18,445 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:05:18,445 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:05:22,335 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:05:22,335 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'c90d41451b10b692', 'timestamp': 1750388722}
2025-06-20 11:05:22,335 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'c90d41451b10b692', 'timestamp': 1750388722}
2025-06-20 11:05:22,335 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: c90d41451b10b692)
2025-06-20 11:05:40,162 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:05:40,162 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:05:48,455 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:05:48,456 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:05:52,343 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:05:52,343 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'e1dfac6876f54454', 'timestamp': 1750388752}
2025-06-20 11:05:52,343 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'e1dfac6876f54454', 'timestamp': 1750388752}
2025-06-20 11:05:52,343 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: e1dfac6876f54454)
2025-06-20 11:06:10,170 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:06:10,170 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:06:18,465 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:06:18,465 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:06:22,346 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:06:22,346 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '03e5238940df6e8e', 'timestamp': 1750388782}
2025-06-20 11:06:22,346 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '03e5238940df6e8e', 'timestamp': 1750388782}
2025-06-20 11:06:22,346 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 03e5238940df6e8e)
2025-06-20 11:06:40,148 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:06:40,148 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:06:48,465 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:06:48,465 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:06:52,361 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:06:52,361 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '5f72c60af38bbec0', 'timestamp': 1750388812}
2025-06-20 11:06:52,361 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '5f72c60af38bbec0', 'timestamp': 1750388812}
2025-06-20 11:06:52,361 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 5f72c60af38bbec0)
2025-06-20 11:07:10,154 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:07:10,155 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:07:12,656 - RelayServer-relay_8012 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 2, 总连接: 8, 中继消息: 8, 传输字节: 504
2025-06-20 11:07:12,656 - RelayServer-relay_8011 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 2, 总连接: 8, 中继消息: 120, 传输字节: 34184
2025-06-20 11:07:12,656 - RelayServer-relay_8013 - INFO - 统计信息 - 运行时间: 1500s, 活跃连接: 2, 总连接: 8, 中继消息: 8, 传输字节: 504
2025-06-20 11:07:12,779 - RelayServer-relay_8012 - INFO - 用户已注销: aaa
2025-06-20 11:07:12,780 - RelayServer-relay_8013 - INFO - 用户已注销: aaa
2025-06-20 11:07:12,780 - RelayServer-relay_8012 - INFO - 客户端已断开: client_24d3a4542bb1139b - Unknown
2025-06-20 11:07:12,780 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'client_24d3a4542bb1139b'
2025-06-20 11:07:12,780 - RelayServer-relay_8012 - INFO - 用户已注销: bbb
2025-06-20 11:07:12,780 - RelayServer-relay_8013 - INFO - 客户端已断开: client_4a36cf892cc38643 - Unknown
2025-06-20 11:07:12,780 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'client_4a36cf892cc38643'
2025-06-20 11:07:12,780 - RelayServer-relay_8013 - INFO - 用户已注销: bbb
2025-06-20 11:07:12,780 - RelayServer-relay_8012 - INFO - 客户端已断开: client_4fc9025f68708a74 - Unknown
2025-06-20 11:07:12,787 - RelayServer-relay_8012 - ERROR - 断开客户端连接时出错: 'client_4fc9025f68708a74'
2025-06-20 11:07:12,788 - RelayServer-relay_8012 - INFO - 清理了 2 个过期连接
2025-06-20 11:07:12,788 - RelayServer-relay_8013 - INFO - 客户端已断开: client_dbf77fb6ad95e798 - Unknown
2025-06-20 11:07:12,788 - RelayServer-relay_8013 - ERROR - 断开客户端连接时出错: 'client_dbf77fb6ad95e798'
2025-06-20 11:07:12,788 - RelayServer-relay_8013 - INFO - 清理了 2 个过期连接
2025-06-20 11:07:18,470 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:07:18,470 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:07:22,363 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:07:22,363 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '9772277eb0d39e79', 'timestamp': 1750388842}
2025-06-20 11:07:22,363 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '9772277eb0d39e79', 'timestamp': 1750388842}
2025-06-20 11:07:22,363 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 9772277eb0d39e79)
2025-06-20 11:07:40,158 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:07:40,158 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:07:48,463 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:07:48,464 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:07:52,375 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:07:52,376 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '4cefc5ea2d2c43cb', 'timestamp': 1750388872}
2025-06-20 11:07:52,376 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '4cefc5ea2d2c43cb', 'timestamp': 1750388872}
2025-06-20 11:07:52,376 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 4cefc5ea2d2c43cb)
2025-06-20 11:08:10,168 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:08:10,168 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:08:18,471 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:08:18,471 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:08:22,378 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:08:22,378 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'cd77c21281e60f70', 'timestamp': 1750388902}
2025-06-20 11:08:22,378 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'cd77c21281e60f70', 'timestamp': 1750388902}
2025-06-20 11:08:22,378 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: cd77c21281e60f70)
2025-06-20 11:08:40,167 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:08:40,167 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:08:48,467 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:08:48,467 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:08:52,392 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:08:52,392 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '3b65172402ef823d', 'timestamp': 1750388932}
2025-06-20 11:08:52,392 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '3b65172402ef823d', 'timestamp': 1750388932}
2025-06-20 11:08:52,392 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 3b65172402ef823d)
2025-06-20 11:09:10,184 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:09:10,184 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:09:18,462 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:09:18,462 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:09:22,387 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:09:22,387 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '77cc51641cea65fa', 'timestamp': 1750388962}
2025-06-20 11:09:22,387 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': '77cc51641cea65fa', 'timestamp': 1750388962}
2025-06-20 11:09:22,387 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: 77cc51641cea65fa)
2025-06-20 11:09:40,187 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_efa0f415c162b0b2
2025-06-20 11:09:40,187 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_efa0f415c162b0b2 -> aaa
2025-06-20 11:09:48,479 - RelayServer-relay_8011 - INFO - 收到控制消息: protocol_message from client_8716fc4754ed06dc
2025-06-20 11:09:48,479 - RelayServer-relay_8011 - INFO - 协议消息已转发: client_8716fc4754ed06dc -> bbb
2025-06-20 11:09:52,397 - RelayServer-relay_8011 - INFO - 收到控制消息: user_lookup from client_efa0f415c162b0b2
2025-06-20 11:09:52,397 - RelayServer-relay_8011 - INFO - 用户查询详情: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'c269b8689ed9604c', 'timestamp': 1750388992}
2025-06-20 11:09:52,397 - RelayServer-relay_8011 - INFO - 开始处理用户查询: {'type': 'user_lookup', 'target_user_id': 'aaa', 'query_id': 'c269b8689ed9604c', 'timestamp': 1750388992}
2025-06-20 11:09:52,397 - RelayServer-relay_8011 - INFO - ✓ 用户查询: aaa -> 在线 (查询ID: c269b8689ed9604c)
2025-06-20 11:10:06,548 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_8716fc4754ed06dc
2025-06-20 11:10:06,548 - RelayServer-relay_8011 - INFO - 用户已注销: aaa
2025-06-20 11:10:06,548 - RelayServer-relay_8011 - INFO - 客户端已断开: client_8716fc4754ed06dc - Unknown
2025-06-20 11:10:07,476 - RelayServer-relay_8011 - INFO - 客户端断开连接: client_efa0f415c162b0b2
2025-06-20 11:10:07,476 - RelayServer-relay_8011 - INFO - 用户已注销: bbb
2025-06-20 11:10:07,476 - RelayServer-relay_8011 - INFO - 客户端已断开: client_efa0f415c162b0b2 - Unknown
2025-06-20 11:10:27,422 - websockets.server - INFO - connection open
2025-06-20 11:10:27,422 - RelayServer-relay_8011 - INFO - ✓ 客户端连接: client_ba3c15e69276d2b2 from ('127.0.0.1', 53458)
2025-06-20 11:10:27,430 - websockets.server - INFO - connection open
2025-06-20 11:10:27,430 - RelayServer-relay_8012 - INFO - ✓ 客户端连接: client_1640b9c95f40f3de from ('127.0.0.1', 53459)
2025-06-20 11:10:27,430 - websockets.server - INFO - connection open
2025-06-20 11:10:27,430 - RelayServer-relay_8013 - INFO - ✓ 客户端连接: client_91e264dbe55906ff from ('127.0.0.1', 53460)
2025-06-20 11:10:27,430 - RelayServer-relay_8011 - INFO - 收到控制消息: register from client_ba3c15e69276d2b2
2025-06-20 11:10:27,438 - RelayServer-relay_8011 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_ba3c15e69276d2b2
2025-06-20 11:10:27,438 - RelayServer-relay_8012 - INFO - 收到控制消息: register from client_1640b9c95f40f3de
2025-06-20 11:10:27,439 - RelayServer-relay_8012 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_1640b9c95f40f3de
2025-06-20 11:10:27,439 - RelayServer-relay_8013 - INFO - 收到控制消息: register from client_91e264dbe55906ff
2025-06-20 11:10:27,440 - RelayServer-relay_8013 - INFO - ✓ 用户注册成功: aaa (aaa) -> client_91e264dbe55906ff
