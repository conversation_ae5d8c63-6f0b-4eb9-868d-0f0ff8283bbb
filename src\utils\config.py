"""
配置管理模块
处理系统配置的加载、保存和验证
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path
import logging


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.configs: Dict[str, Dict] = {}
        self.logger = logging.getLogger(__name__)
    
    def load_config(self, config_name: str, default_config: Dict = None) -> Dict:
        """加载配置文件"""
        config_file = self.config_dir / f"{config_name}.json"
        
        try:
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.configs[config_name] = config
                self.logger.info(f"已加载配置: {config_name}")
                return config
            else:
                if default_config:
                    self.save_config(config_name, default_config)
                    self.configs[config_name] = default_config
                    self.logger.info(f"已创建默认配置: {config_name}")
                    return default_config
                else:
                    self.logger.warning(f"配置文件不存在且无默认配置: {config_name}")
                    return {}
        except Exception as e:
            self.logger.error(f"加载配置失败 {config_name}: {e}")
            return default_config or {}
    
    def save_config(self, config_name: str, config: Dict) -> bool:
        """保存配置文件"""
        config_file = self.config_dir / f"{config_name}.json"
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            self.configs[config_name] = config
            self.logger.info(f"已保存配置: {config_name}")
            return True
        except Exception as e:
            self.logger.error(f"保存配置失败 {config_name}: {e}")
            return False
    
    def get_config(self, config_name: str) -> Optional[Dict]:
        """获取配置"""
        return self.configs.get(config_name)
    
    def update_config(self, config_name: str, updates: Dict) -> bool:
        """更新配置"""
        if config_name in self.configs:
            self.configs[config_name].update(updates)
            return self.save_config(config_name, self.configs[config_name])
        return False
    
    def get_value(self, config_name: str, key: str, default=None):
        """获取配置值"""
        config = self.get_config(config_name)
        if config:
            return config.get(key, default)
        return default
    
    def set_value(self, config_name: str, key: str, value) -> bool:
        """设置配置值"""
        if config_name not in self.configs:
            self.configs[config_name] = {}
        
        self.configs[config_name][key] = value
        return self.save_config(config_name, self.configs[config_name])


# 默认配置
DEFAULT_CLIENT_CONFIG = {
    "user_id": "",
    "relay_servers": [
        "ws://localhost:8001",
        "ws://localhost:8002",
        "ws://localhost:8003"
    ],
    "auto_connect": False,
    "identity_rotation_interval": 3600,
    "circuit_length": 3,
    "use_dummy_traffic": True,
    "dummy_traffic_ratio": 0.3,
    "connection_timeout": 300,
    "heartbeat_interval": 30,
    "max_message_size": 1024 * 1024,
    "chunk_size": 64 * 1024,
    "contacts": {},
    "gui": {
        "theme": "default",
        "font_size": 10,
        "auto_save_chat": True,
        "show_notifications": True
    },
    "security": {
        "auto_rotate_identity": True,
        "use_traffic_obfuscation": True,
        "min_packet_size": 1024,
        "max_packet_size": 8192,
        "key_rotation_interval": 86400
    }
}

DEFAULT_SERVER_CONFIG = {
    "host": "localhost",
    "port": 8001,
    "server_id": "",
    "max_connections": 1000,
    "connection_timeout": 300,
    "max_message_size": 1024 * 1024,
    "log_level": "INFO",
    "enable_stats": True,
    "stats_interval": 300,
    "cleanup_interval": 60,
    "dummy_traffic": {
        "enabled": True,
        "interval_min": 10,
        "interval_max": 40,
        "min_clients": 2
    },
    "rate_limiting": {
        "enabled": True,
        "max_messages_per_minute": 60,
        "max_bytes_per_minute": 1024 * 1024
    }
}

DEFAULT_CRYPTO_CONFIG = {
    "symmetric": {
        "algorithm": "AES-256-GCM",
        "key_size": 32,
        "nonce_size": 12
    },
    "asymmetric": {
        "rsa_key_size": 4096,
        "ecc_curve": "SECP384R1"
    },
    "key_derivation": {
        "pbkdf2_iterations": 100000,
        "scrypt_n": 16384,
        "scrypt_r": 8,
        "scrypt_p": 1
    },
    "key_management": {
        "default_expiry": 86400,
        "cleanup_interval": 3600,
        "backup_interval": 86400,
        "master_key_rotation": 2592000  # 30天
    }
}

# 全局配置管理器
config_manager = ConfigManager()


def load_all_configs():
    """加载所有配置"""
    config_manager.load_config("client", DEFAULT_CLIENT_CONFIG)
    config_manager.load_config("server", DEFAULT_SERVER_CONFIG)
    config_manager.load_config("crypto", DEFAULT_CRYPTO_CONFIG)


def get_client_config() -> Dict:
    """获取客户端配置"""
    return config_manager.get_config("client") or DEFAULT_CLIENT_CONFIG


def get_server_config() -> Dict:
    """获取服务器配置"""
    return config_manager.get_config("server") or DEFAULT_SERVER_CONFIG


def get_crypto_config() -> Dict:
    """获取加密配置"""
    return config_manager.get_config("crypto") or DEFAULT_CRYPTO_CONFIG


def update_client_config(updates: Dict) -> bool:
    """更新客户端配置"""
    return config_manager.update_config("client", updates)


def update_server_config(updates: Dict) -> bool:
    """更新服务器配置"""
    return config_manager.update_config("server", updates)


def validate_config(config: Dict, config_type: str) -> bool:
    """验证配置"""
    if config_type == "client":
        required_keys = ["relay_servers", "circuit_length"]
        for key in required_keys:
            if key not in config:
                return False
        
        # 验证中继服务器列表
        if not isinstance(config["relay_servers"], list) or len(config["relay_servers"]) == 0:
            return False
        
        # 验证电路长度
        if not isinstance(config["circuit_length"], int) or config["circuit_length"] < 2:
            return False
    
    elif config_type == "server":
        required_keys = ["host", "port", "max_connections"]
        for key in required_keys:
            if key not in config:
                return False
        
        # 验证端口
        if not isinstance(config["port"], int) or not (1 <= config["port"] <= 65535):
            return False
        
        # 验证最大连接数
        if not isinstance(config["max_connections"], int) or config["max_connections"] <= 0:
            return False
    
    return True


# 示例使用
if __name__ == "__main__":
    # 加载所有配置
    load_all_configs()
    
    # 获取配置
    client_config = get_client_config()
    server_config = get_server_config()
    crypto_config = get_crypto_config()
    
    print("客户端配置:")
    print(json.dumps(client_config, ensure_ascii=False, indent=2))
    
    print("\n服务器配置:")
    print(json.dumps(server_config, ensure_ascii=False, indent=2))
    
    print("\n加密配置:")
    print(json.dumps(crypto_config, ensure_ascii=False, indent=2))
    
    # 验证配置
    print(f"\n客户端配置验证: {validate_config(client_config, 'client')}")
    print(f"服务器配置验证: {validate_config(server_config, 'server')}")
    
    # 更新配置示例
    update_client_config({"user_id": "test_user"})
    print(f"\n更新后的用户ID: {config_manager.get_value('client', 'user_id')}")
