"""网络配置模块"""

# 默认配置 (本地测试 - 启用安全特性)
DEFAULT_NETWORK_CONFIG = {
    'relay_servers': [
        'ws://localhost:8011',
        'ws://localhost:8012',
        'ws://localhost:8013'
    ],
    'use_wss': False,  # 本地测试使用ws://
    'use_onion_routing': True,  # 启用洋葱路由
    'circuit_length': 3,
    'connection_timeout': 300,
    'heartbeat_interval': 30,
    'identity_rotation_interval': 3600,
    'max_message_size': 1024 * 1024,  # 1MB
    'chunk_size': 64 * 1024,  # 64KB
    'dummy_traffic_ratio': 0.3,
    'min_packet_size': 1024,
    'max_packet_size': 8192
} 