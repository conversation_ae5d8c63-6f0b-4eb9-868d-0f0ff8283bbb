from dataclasses import dataclass, field
from typing import Optional
import time
import base64
import json

@dataclass
class MessageHeader:
    message_type: str
    sender_id: str
    target_id: str
    timestamp: int = field(default_factory=lambda: int(time.time()))
    session_id: Optional[str] = None
    chunk_index: Optional[int] = None
    total_chunks: Optional[int] = None

    def to_dict(self):
        return {
            'message_type': self.message_type,
            'sender_id': self.sender_id,
            'target_id': self.target_id,
            'timestamp': self.timestamp,
            'session_id': self.session_id,
            'chunk_index': self.chunk_index,
            'total_chunks': self.total_chunks
        }

    @staticmethod
    def from_dict(data):
        return MessageHeader(
            message_type=data['message_type'],
            sender_id=data['sender_id'],
            target_id=data['target_id'],
            timestamp=data.get('timestamp', int(time.time())),
            session_id=data.get('session_id'),
            chunk_index=data.get('chunk_index'),
            total_chunks=data.get('total_chunks')
        )

class ProtocolMessage:
    def __init__(self, msg_type: str, sender: str, receiver: str, payload: dict):
        self.msg_type = msg_type
        self.sender = sender
        self.receiver = receiver
        self.payload = payload

    def to_dict(self):
        return {
            'msg_type': self.msg_type,
            'sender': self.sender,
            'receiver': self.receiver,
            'payload': self.payload
        }

    @staticmethod
    def from_dict(d: dict):
        return ProtocolMessage(
            d['msg_type'],
            d['sender'],
            d['receiver'],
            d['payload']
        )

    def serialize(self) -> str:
        return base64.b64encode(json.dumps(self.to_dict()).encode('utf-8')).decode('ascii')

    @staticmethod
    def deserialize(s: str):
        d = json.loads(base64.b64decode(s.encode('ascii')).decode('utf-8'))
        return ProtocolMessage.from_dict(d) 