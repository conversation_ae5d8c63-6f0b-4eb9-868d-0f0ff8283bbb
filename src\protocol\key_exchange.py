"""
密钥交换实现
提供端到端加密的密钥交换功能
"""

import os
import time
import base64
import secrets
from typing import Dict, Optional, Tuple
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.primitives.ciphers.aead import AESGCM


class KeyExchange:
    """密钥交换实现"""
    
    def __init__(self):
        self.key_pairs: Dict[str, Tuple[rsa.RSAPrivateKey, rsa.RSAPublicKey]] = {}
        self.public_keys: Dict[str, rsa.RSAPublicKey] = {}
        self.session_keys: Dict[str, bytes] = {}
        self.session_ids: Dict[str, str] = {}
    
    def generate_keypair(self, user_id: str) -> str:
        """为用户生成RSA密钥对，返回base64字符串"""
        # 检查是否已经有密钥对
        if user_id in self.key_pairs:
            private_key, public_key = self.key_pairs[user_id]
        else:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048  # 确保使用2048位密钥
            )
            public_key = private_key.public_key()
            self.key_pairs[user_id] = (private_key, public_key)

        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return base64.b64encode(public_bytes).decode('ascii')
    
    def store_public_key(self, user_id: str, public_key_b64: str) -> None:
        public_key_bytes = base64.b64decode(public_key_b64)
        public_key = serialization.load_pem_public_key(public_key_bytes)
        self.public_keys[user_id] = public_key
    
    def perform_exchange(self, user_id: str, peer_id: str, peer_public_key_b64: str) -> Tuple[str, str]:
        """执行密钥交换，生成会话密钥并用对方公钥加密，全部用base64字符串"""
        try:
            self.store_public_key(peer_id, peer_public_key_b64)
            # 生成会话密钥
            session_key = secrets.token_bytes(32)
            session_id = f"session_{secrets.token_hex(8)}"
            # 用对方公钥加密会话密钥
            peer_public_key = self.public_keys[peer_id]

            encrypted_session_key = peer_public_key.encrypt(
                session_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )

            # 存储会话信息
            self.session_keys[session_id] = session_key
            self.session_ids[f"{user_id}_{peer_id}"] = session_id
            return base64.b64encode(encrypted_session_key).decode('ascii'), session_id

        except Exception as e:
            raise ValueError(f"密钥交换执行失败: {str(e)}")
    
    def complete_exchange(self, user_id: str, peer_id: str, encrypted_session_key_b64: str, session_id: str = None) -> str:
        """用自己私钥解密会话密钥，全部用base64字符串"""
        try:
            encrypted_session_key = base64.b64decode(encrypted_session_key_b64)
            private_key, _ = self.key_pairs[user_id]

            # 检查密文长度是否正确
            key_size = private_key.key_size // 8  # 转换为字节
            ciphertext_size = len(encrypted_session_key)

            if ciphertext_size != key_size:
                raise ValueError(f"密文长度 ({ciphertext_size}) 与密钥大小 ({key_size}) 不匹配")

            session_key = private_key.decrypt(
                encrypted_session_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            if session_id is None:
                session_id = f"session_{secrets.token_hex(8)}"
            self.session_keys[session_id] = session_key
            self.session_ids[f"{user_id}_{peer_id}"] = session_id
            return session_id

        except Exception as e:
            # 记录错误信息但不打印敏感信息
            raise ValueError(f"密钥交换完成失败: {str(e)}")
    
    def get_session_key(self, session_id: str) -> Optional[bytes]:
        return self.session_keys.get(session_id)
    
    def get_session_id(self, user_id: str, peer_id: str) -> Optional[str]:
        return self.session_ids.get(f"{user_id}_{peer_id}")
    
    def has_session(self, session_id: str) -> bool:
        return session_id in self.session_keys
    
    def clear_session(self, session_id: str) -> None:
        if session_id in self.session_keys:
            del self.session_keys[session_id]
        for key in list(self.session_ids.keys()):
            if self.session_ids[key] == session_id:
                del self.session_ids[key]
    
    def cleanup_expired_sessions(self) -> int:
        return 0 