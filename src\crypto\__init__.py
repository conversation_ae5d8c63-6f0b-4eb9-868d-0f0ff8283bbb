"""
匿名加密通讯系统 - 加密模块
提供完整的加密、密钥管理和安全通信功能
"""

from .symmetric import SymmetricCrypto
from .asymmetric import RSACrypto, ECCCrypto, HybridCrypto
from .key_exchange import ECDH<PERSON>eyExchange, Session<PERSON>eyManager, PerfectForwardSecrecy
from .hashing import SecureHash, MessageAuth, KeyDerivation, IntegrityChecker
from .key_manager import SecureKeyStorage, KeyManager

__all__ = [
    # 对称加密
    'SymmetricCrypto',
    
    # 非对称加密
    'RSACrypto',
    'ECCCrypto', 
    'HybridCrypto',
    
    # 密钥交换
    'ECDHKeyExchange',
    'SessionKeyManager',
    'PerfectForwardSecrecy',
    
    # 哈希和认证
    'SecureHash',
    'MessageAuth',
    'KeyDerivation',
    'IntegrityChecker',
    
    # 密钥管理
    'SecureKeyStorage',
    'KeyManager'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Anonymous Crypto Chat Team"
__description__ = "Multi-layer encryption and anonymization system"

# 默认配置
DEFAULT_CONFIG = {
    'symmetric': {
        'algorithm': 'AES-256-GCM',
        'key_size': 32,
        'nonce_size': 12
    },
    'asymmetric': {
        'rsa_key_size': 4096,
        'ecc_curve': 'SECP384R1'
    },
    'key_derivation': {
        'pbkdf2_iterations': 100000,
        'scrypt_n': 2**14,
        'scrypt_r': 8,
        'scrypt_p': 1
    },
    'key_management': {
        'default_expiry': 86400,  # 24小时
        'cleanup_interval': 3600,  # 1小时
        'backup_interval': 86400   # 24小时
    }
}


def get_version():
    """获取版本信息"""
    return __version__


def get_default_config():
    """获取默认配置"""
    return DEFAULT_CONFIG.copy()


# 便捷函数
def create_crypto_suite(master_password: str, storage_path: str = "keystore.db"):
    """创建完整的加密套件"""
    key_manager = KeyManager(storage_path)
    if not key_manager.initialize(master_password):
        raise RuntimeError("无法初始化密钥管理器")
    
    return {
        'key_manager': key_manager,
        'symmetric': SymmetricCrypto(),
        'rsa': RSACrypto(),
        'ecc': ECCCrypto(),
        'hash': SecureHash(),
        'auth': MessageAuth(),
        'session_manager': SessionKeyManager(),
        'integrity': IntegrityChecker()
    }


def quick_encrypt_message(message: str, password: str) -> str:
    """快速加密消息（使用密码派生密钥）"""
    crypto = SymmetricCrypto()
    key, salt = crypto.derive_key_from_password(password)
    encrypted = crypto.encrypt_message(message, key)
    
    # 将盐值和加密消息组合
    import base64
    salt_b64 = base64.b64encode(salt).decode('ascii')
    return f"{salt_b64}:{encrypted}"


def quick_decrypt_message(encrypted_data: str, password: str) -> str:
    """快速解密消息"""
    import base64
    
    try:
        salt_b64, encrypted_message = encrypted_data.split(':', 1)
        salt = base64.b64decode(salt_b64)
        
        crypto = SymmetricCrypto()
        key, _ = crypto.derive_key_from_password(password, salt)
        return crypto.decrypt_message(encrypted_message, key)
    except Exception as e:
        raise RuntimeError(f"解密失败: {e}")


# 安全工具函数
def secure_random_bytes(length: int) -> bytes:
    """生成安全随机字节"""
    import secrets
    return secrets.token_bytes(length)


def secure_random_string(length: int) -> str:
    """生成安全随机字符串"""
    import secrets
    import string
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def constant_time_compare(a: bytes, b: bytes) -> bool:
    """常数时间比较（防止时序攻击）"""
    import hmac
    return hmac.compare_digest(a, b)


# 测试函数
def run_crypto_tests():
    """运行加密模块测试"""
    print("=== 加密模块测试 ===")
    
    # 测试对称加密
    print("测试对称加密...")
    sym_crypto = SymmetricCrypto()
    key = sym_crypto.generate_key()
    message = "Hello, World!"
    encrypted = sym_crypto.encrypt_message(message, key)
    decrypted = sym_crypto.decrypt_message(encrypted, key)
    assert message == decrypted, "对称加密测试失败"
    print("✓ 对称加密测试通过")
    
    # 测试非对称加密
    print("测试RSA加密...")
    rsa_crypto = RSACrypto()
    private_key, public_key = rsa_crypto.generate_keypair()
    test_data = b"Test data"
    signature = rsa_crypto.sign(test_data, private_key)
    is_valid = rsa_crypto.verify(test_data, signature, public_key)
    assert is_valid, "RSA签名测试失败"
    print("✓ RSA加密测试通过")
    
    # 测试哈希
    print("测试哈希函数...")
    hash_result = SecureHash.sha256("test")
    assert len(hash_result) == 32, "SHA-256哈希长度错误"
    print("✓ 哈希函数测试通过")
    
    # 测试HMAC
    print("测试HMAC...")
    auth = MessageAuth()
    key = auth.generate_key()
    mac = auth.hmac_sha256("test message", key)
    is_valid = auth.verify_hmac("test message", mac, key)
    assert is_valid, "HMAC验证失败"
    print("✓ HMAC测试通过")
    
    # 测试密钥交换
    print("测试密钥交换...")
    ecdh = ECDHKeyExchange()
    alice_private, alice_public = ecdh.generate_keypair()
    bob_private, bob_public = ecdh.generate_keypair()
    
    alice_shared = ecdh.perform_exchange(alice_private, bob_public)
    bob_shared = ecdh.perform_exchange(bob_private, alice_public)
    assert alice_shared == bob_shared, "密钥交换失败"
    print("✓ 密钥交换测试通过")
    
    print("=== 所有测试通过 ===")


if __name__ == "__main__":
    # 运行测试
    run_crypto_tests()
    
    # 演示快速加密
    print("\n=== 快速加密演示 ===")
    password = "my_password"
    message = "这是一条秘密消息"
    
    encrypted = quick_encrypt_message(message, password)
    print(f"加密后: {encrypted}")
    
    decrypted = quick_decrypt_message(encrypted, password)
    print(f"解密后: {decrypted}")
    
    # 演示加密套件
    print("\n=== 加密套件演示 ===")
    try:
        suite = create_crypto_suite("master_password_123", "demo_keystore.db")
        print("✓ 加密套件创建成功")
        print(f"可用组件: {list(suite.keys())}")
    except Exception as e:
        print(f"✗ 加密套件创建失败: {e}")
