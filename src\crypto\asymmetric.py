"""
非对称加密模块 - 支持RSA和ECC加密
用于密钥交换、数字签名和身份验证
"""

import secrets
from typing import <PERSON><PERSON>, Optional
from cryptography.hazmat.primitives.asymmetric import rsa, ec, padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.serialization import Encoding, PrivateFormat, PublicFormat, NoEncryption
import base64


class RSACrypto:
    """RSA非对称加密类"""
    
    def __init__(self, key_size: int = 4096):
        self.key_size = key_size
        self.padding_scheme = padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
        self.signature_padding = padding.PSS(
            mgf=padding.MGF1(hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH
        )
    
    def generate_keypair(self) -> Tuple[rsa.RSAPrivateKey, rsa.RSAPublicKey]:
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=self.key_size,
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    def serialize_private_key(self, private_key: rsa.RSAPrivateKey, password: Optional[str] = None) -> bytes:
        """序列化私钥"""
        encryption_algorithm = NoEncryption()
        if password:
            encryption_algorithm = serialization.BestAvailableEncryption(password.encode())
        
        return private_key.private_bytes(
            encoding=Encoding.PEM,
            format=PrivateFormat.PKCS8,
            encryption_algorithm=encryption_algorithm
        )
    
    def serialize_public_key(self, public_key: rsa.RSAPublicKey) -> bytes:
        """序列化公钥"""
        return public_key.public_bytes(
            encoding=Encoding.PEM,
            format=PublicFormat.SubjectPublicKeyInfo
        )
    
    def load_private_key(self, key_data: bytes, password: Optional[str] = None) -> rsa.RSAPrivateKey:
        """加载私钥"""
        password_bytes = password.encode() if password else None
        return serialization.load_pem_private_key(key_data, password=password_bytes)
    
    def load_public_key(self, key_data: bytes) -> rsa.RSAPublicKey:
        """加载公钥"""
        return serialization.load_pem_public_key(key_data)
    
    def encrypt(self, plaintext: bytes, public_key: rsa.RSAPublicKey) -> bytes:
        """RSA加密"""
        max_chunk_size = (self.key_size // 8) - 2 * (hashes.SHA256().digest_size) - 2
        
        if len(plaintext) <= max_chunk_size:
            return public_key.encrypt(plaintext, self.padding_scheme)
        else:
            # 对于大数据，分块加密
            encrypted_chunks = []
            for i in range(0, len(plaintext), max_chunk_size):
                chunk = plaintext[i:i + max_chunk_size]
                encrypted_chunk = public_key.encrypt(chunk, self.padding_scheme)
                encrypted_chunks.append(encrypted_chunk)
            return b''.join(encrypted_chunks)
    
    def decrypt(self, ciphertext: bytes, private_key: rsa.RSAPrivateKey) -> bytes:
        """RSA解密"""
        chunk_size = self.key_size // 8
        
        if len(ciphertext) <= chunk_size:
            return private_key.decrypt(ciphertext, self.padding_scheme)
        else:
            # 对于大数据，分块解密
            decrypted_chunks = []
            for i in range(0, len(ciphertext), chunk_size):
                chunk = ciphertext[i:i + chunk_size]
                decrypted_chunk = private_key.decrypt(chunk, self.padding_scheme)
                decrypted_chunks.append(decrypted_chunk)
            return b''.join(decrypted_chunks)
    
    def sign(self, message: bytes, private_key: rsa.RSAPrivateKey) -> bytes:
        """RSA数字签名"""
        return private_key.sign(message, self.signature_padding, hashes.SHA256())
    
    def verify(self, message: bytes, signature: bytes, public_key: rsa.RSAPublicKey) -> bool:
        """验证RSA数字签名"""
        try:
            public_key.verify(signature, message, self.signature_padding, hashes.SHA256())
            return True
        except Exception:
            return False


class ECCCrypto:
    """ECC椭圆曲线加密类"""
    
    def __init__(self, curve=ec.SECP384R1()):
        self.curve = curve
    
    def generate_keypair(self) -> Tuple[ec.EllipticCurvePrivateKey, ec.EllipticCurvePublicKey]:
        """生成ECC密钥对"""
        private_key = ec.generate_private_key(self.curve)
        public_key = private_key.public_key()
        return private_key, public_key
    
    def serialize_private_key(self, private_key: ec.EllipticCurvePrivateKey, password: Optional[str] = None) -> bytes:
        """序列化ECC私钥"""
        encryption_algorithm = NoEncryption()
        if password:
            encryption_algorithm = serialization.BestAvailableEncryption(password.encode())
        
        return private_key.private_bytes(
            encoding=Encoding.PEM,
            format=PrivateFormat.PKCS8,
            encryption_algorithm=encryption_algorithm
        )
    
    def serialize_public_key(self, public_key: ec.EllipticCurvePublicKey) -> bytes:
        """序列化ECC公钥"""
        return public_key.public_bytes(
            encoding=Encoding.PEM,
            format=PublicFormat.SubjectPublicKeyInfo
        )
    
    def load_private_key(self, key_data: bytes, password: Optional[str] = None) -> ec.EllipticCurvePrivateKey:
        """加载ECC私钥"""
        password_bytes = password.encode() if password else None
        return serialization.load_pem_private_key(key_data, password=password_bytes)
    
    def load_public_key(self, key_data: bytes) -> ec.EllipticCurvePublicKey:
        """加载ECC公钥"""
        return serialization.load_pem_public_key(key_data)
    
    def sign(self, message: bytes, private_key: ec.EllipticCurvePrivateKey) -> bytes:
        """ECC数字签名"""
        return private_key.sign(message, ec.ECDSA(hashes.SHA256()))
    
    def verify(self, message: bytes, signature: bytes, public_key: ec.EllipticCurvePublicKey) -> bool:
        """验证ECC数字签名"""
        try:
            public_key.verify(signature, message, ec.ECDSA(hashes.SHA256()))
            return True
        except Exception:
            return False


class HybridCrypto:
    """混合加密系统 - 结合RSA/ECC和AES的优势"""
    
    def __init__(self, use_ecc: bool = True):
        self.use_ecc = use_ecc
        if use_ecc:
            self.asymmetric = ECCCrypto()
        else:
            self.asymmetric = RSACrypto()
    
    def generate_keypair(self):
        """生成密钥对"""
        return self.asymmetric.generate_keypair()
    
    def encrypt_with_public_key(self, plaintext: bytes, public_key) -> Tuple[bytes, bytes]:
        """
        使用公钥加密（混合加密）
        返回: (加密的AES密钥, AES加密的数据)
        """
        from .symmetric import SymmetricCrypto
        
        # 生成随机AES密钥
        sym_crypto = SymmetricCrypto()
        aes_key = sym_crypto.generate_key()
        
        # 使用AES加密数据
        encrypted_data, nonce = sym_crypto.encrypt(plaintext, aes_key)
        
        # 使用公钥加密AES密钥
        if self.use_ecc:
            # ECC不能直接加密，需要使用ECIES或其他方案
            # 这里简化处理，实际应用中需要实现完整的ECIES
            raise NotImplementedError("ECC加密需要实现ECIES方案")
        else:
            encrypted_key = self.asymmetric.encrypt(aes_key, public_key)
        
        # 组合nonce、加密的密钥和加密的数据
        result = nonce + encrypted_key + encrypted_data
        return result, aes_key
    
    def decrypt_with_private_key(self, encrypted_data: bytes, private_key) -> bytes:
        """使用私钥解密（混合解密）"""
        from .symmetric import SymmetricCrypto
        
        sym_crypto = SymmetricCrypto()
        
        # 分离nonce、加密的密钥和加密的数据
        nonce = encrypted_data[:sym_crypto.nonce_size]
        
        if self.use_ecc:
            raise NotImplementedError("ECC解密需要实现ECIES方案")
        else:
            key_size = private_key.key_size // 8
            encrypted_key = encrypted_data[sym_crypto.nonce_size:sym_crypto.nonce_size + key_size]
            ciphertext = encrypted_data[sym_crypto.nonce_size + key_size:]
            
            # 解密AES密钥
            aes_key = self.asymmetric.decrypt(encrypted_key, private_key)
        
        # 使用AES密钥解密数据
        plaintext = sym_crypto.decrypt(ciphertext, aes_key, nonce)
        return plaintext


# 示例使用
if __name__ == "__main__":
    # RSA示例
    print("=== RSA加密示例 ===")
    rsa_crypto = RSACrypto()
    rsa_private, rsa_public = rsa_crypto.generate_keypair()
    
    message = b"Hello, RSA!"
    encrypted = rsa_crypto.encrypt(message, rsa_public)
    decrypted = rsa_crypto.decrypt(encrypted, rsa_private)
    print(f"原文: {message}")
    print(f"解密: {decrypted}")
    
    # 数字签名
    signature = rsa_crypto.sign(message, rsa_private)
    is_valid = rsa_crypto.verify(message, signature, rsa_public)
    print(f"签名验证: {is_valid}")
    
    # ECC示例
    print("\n=== ECC签名示例 ===")
    ecc_crypto = ECCCrypto()
    ecc_private, ecc_public = ecc_crypto.generate_keypair()
    
    ecc_signature = ecc_crypto.sign(message, ecc_private)
    ecc_valid = ecc_crypto.verify(message, ecc_signature, ecc_public)
    print(f"ECC签名验证: {ecc_valid}")
    
    # 密钥序列化
    print("\n=== 密钥序列化示例 ===")
    private_pem = rsa_crypto.serialize_private_key(rsa_private)
    public_pem = rsa_crypto.serialize_public_key(rsa_public)
    print(f"私钥PEM长度: {len(private_pem)}")
    print(f"公钥PEM长度: {len(public_pem)}")
