#!/usr/bin/env python3
"""
启动中继服务器
"""

import asyncio
import websockets
import json
import logging
import time
import ssl
import os
from typing import Dict, Set

# 设置日志级别
logging.basicConfig(level=logging.INFO)

class SimpleRelayServer:
    def __init__(self, server_id: str, host: str, port: int):
        self.server_id = server_id
        self.host = host
        self.port = port
        self.clients = {}  # client_id -> websocket
        self.client_to_user = {}  # client_id -> user_id
        self.anonymous_users = {}  # user_id -> client_id
        
        # 设置日志
        self.logger = logging.getLogger(f"RelayServer-{server_id}")
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    async def start(self):
        """启动服务器"""
        self.logger.info(f"启动中继服务器 {self.server_id} on {self.host}:{self.port}")
        
        # 修复WebSocket处理器函数签名
        async def handle_client(websocket):
            await self._handle_client_connection(websocket, None)
        
        server = await websockets.serve(handle_client, self.host, self.port)
        self.logger.info(f"✓ 中继服务器已启动: ws://{self.host}:{self.port}")
        
        # 定期统计
        asyncio.create_task(self._periodic_stats())
        
        await server.wait_closed()

    async def _handle_client_connection(self, websocket, path=None):
        """处理客户端连接"""
        client_id = f"client_{id(websocket):016x}"
        self.clients[client_id] = websocket
        
        try:
            self.logger.info(f"✓ 客户端连接: {client_id} from {websocket.remote_address}")
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(client_id, data)
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON解析错误: {e}")
                    await self._send_error(client_id, "INVALID_JSON", "Invalid JSON format")
                except Exception as e:
                    self.logger.error(f"处理消息时出错: {e}")
                    await self._send_error(client_id, "MESSAGE_ERROR", str(e))
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"客户端正常断开: {client_id}")
        except Exception as e:
            self.logger.error(f"客户端连接错误: {e}")
        finally:
            await self._cleanup_client(client_id)

    async def _handle_message(self, client_id: str, data: Dict):
        """处理客户端消息"""
        try:
            message_type = data.get('type')
            self.logger.debug(f"处理消息类型: {message_type} from {client_id}")
            
            if message_type == 'register':
                await self._handle_register(client_id, data)
            elif message_type == 'anonymous_message':
                await self._handle_anonymous_message(client_id, data)
            elif message_type == 'encrypted_message':
                await self._handle_encrypted_message(client_id, data)
            elif message_type == 'encrypted_file':
                await self._handle_encrypted_file(client_id, data)
            elif message_type == 'key_exchange_request':
                await self._handle_key_exchange(client_id, data)
            elif message_type == 'key_exchange_response':
                await self._handle_key_exchange(client_id, data)
            elif message_type == 'onion_packet':
                await self._handle_onion_packet(client_id, data)
            elif message_type == 'user_lookup':
                await self._handle_user_lookup(client_id, data)
            elif message_type == 'ping':
                await self._handle_ping(client_id, data)
            else:
                await self._send_error(client_id, "UNKNOWN_MESSAGE_TYPE", f"Unknown message type: {message_type}")
        except Exception as e:
            self.logger.error(f"处理消息时出错: {e}")
            await self._send_error(client_id, "MESSAGE_PROCESSING_ERROR", str(e))

    async def _handle_register(self, client_id: str, data: Dict):
        """处理用户注册"""
        user_id = data.get('user_id')
        display_name = data.get('display_name', user_id)
        
        if not user_id:
            await self._send_error(client_id, "INVALID_USER_ID", "User ID is required")
            return
        
        # 注册用户
        self.client_to_user[client_id] = user_id
        self.anonymous_users[user_id] = client_id
        
        # 发送注册成功响应
        response = {
            'type': 'register_success',
            'user_id': user_id,
            'display_name': display_name,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(response))
        self.logger.info(f"✓ 用户注册成功: {user_id} -> {client_id}")

    async def _handle_anonymous_message(self, client_id: str, data: Dict):
        """处理匿名消息"""
        await self._relay_message(client_id, data, 'anonymous_message_received')

    async def _handle_encrypted_message(self, client_id: str, data: Dict):
        """处理加密消息"""
        await self._relay_message(client_id, data, 'encrypted_message_received')

    async def _handle_encrypted_file(self, client_id: str, data: Dict):
        """处理加密文件"""
        await self._relay_message(client_id, data, 'encrypted_file_received')

    async def _handle_key_exchange(self, client_id: str, data: Dict):
        """处理密钥交换请求"""
        try:
            target_user_id = data.get('target_user_id')
            public_key = data.get('public_key')
            sender_user_id = self.client_to_user.get(client_id)
            
            if not target_user_id or not public_key or not sender_user_id:
                await self._send_error(client_id, "INVALID_KEY_EXCHANGE", "Missing required fields")
                return
            
            # 查找目标用户
            target_client_id = self.anonymous_users.get(target_user_id)
            if not target_client_id:
                await self._send_error(client_id, "TARGET_OFFLINE", f"User {target_user_id} is offline")
                return
            
            # 转发公钥交换消息
            key_exchange_message = {
                'type': 'key_exchange_response' if data.get('type') == 'key_exchange_response' else 'key_exchange_request',
                'sender_user_id': sender_user_id,
                'public_key': public_key,
                'timestamp': int(time.time())
            }
            
            await self._send_to_client(target_client_id, json.dumps(key_exchange_message))
            self.logger.info(f"✓ 公钥交换消息已转发: {sender_user_id} -> {target_user_id}")
            
        except Exception as e:
            self.logger.error(f"处理密钥交换失败: {e}")
            await self._send_error(client_id, "KEY_EXCHANGE_ERROR", str(e))

    async def _handle_onion_packet(self, client_id: str, data: Dict):
        """处理洋葱路由包 - 改进的多层解密"""
        try:
            import base64
            import json
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

            layers = data.get('layers', 0)
            entry_virtual_node = data.get('entry_virtual_node')
            relay_mapping = data.get('relay_mapping', {})
            packet_data = data.get('packet_data')

            self.logger.info(f"收到洋葱数据包: {layers}层加密, 入口虚拟节点: {entry_virtual_node}")

            if not packet_data:
                await self._send_error(client_id, "INVALID_ONION_PACKET", "Missing packet data")
                return

            # 解码数据包
            try:
                current_data = base64.b64decode(packet_data)
            except Exception as e:
                await self._send_error(client_id, "INVALID_PACKET_DATA", f"Cannot decode packet: {e}")
                return

            # 解析当前层的加密信息
            try:
                layer_info = json.loads(current_data.decode('utf-8'))
                layer_key = base64.b64decode(layer_info['key'])
                layer_iv = base64.b64decode(layer_info['iv'])
                encrypted_data = base64.b64decode(layer_info['data'])
            except Exception as e:
                await self._send_error(client_id, "INVALID_LAYER_FORMAT", f"Cannot parse layer: {e}")
                return

            # 解密当前层
            try:
                cipher = Cipher(algorithms.AES(layer_key), modes.CBC(layer_iv))
                decryptor = cipher.decryptor()
                decrypted_padded = decryptor.update(encrypted_data) + decryptor.finalize()

                # 去除填充
                padding_length = decrypted_padded[-1]
                decrypted_data = decrypted_padded[:-padding_length]

                self.logger.info(f"✓ 洋葱层解密成功")

            except Exception as e:
                await self._send_error(client_id, "DECRYPTION_FAILED", f"Cannot decrypt layer: {e}")
                return

            # 解析路由头
            try:
                header_length = int.from_bytes(decrypted_data[:4], 'big')
                header_data = decrypted_data[4:4 + header_length]
                payload_data = decrypted_data[4 + header_length:]

                routing_header = json.loads(header_data.decode('utf-8'))
                action = routing_header.get('action')
                destination = routing_header.get('destination')
                next_virtual_node = routing_header.get('next_virtual_node')
                layer_num = routing_header.get('layer', 0)

                self.logger.info(f"路由头解析: 动作={action}, 目标={destination}, 下一虚拟节点={next_virtual_node}, 层数={layer_num}")

            except Exception as e:
                await self._send_error(client_id, "INVALID_ROUTING_HEADER", f"Cannot parse routing header: {e}")
                return

            if action == "deliver" and destination:
                # 到达最终目的地，解析并转发消息
                try:
                    final_message = json.loads(payload_data.decode('utf-8'))
                    target_user_id = final_message.get('target_user_id')

                    if not target_user_id:
                        await self._send_error(client_id, "NO_TARGET_USER", "No target user in final message")
                        return

                    # 查找目标用户
                    target_client_id = self.anonymous_users.get(target_user_id)
                    if not target_client_id:
                        await self._send_error(client_id, "TARGET_OFFLINE", f"User {target_user_id} is offline")
                        return

                    # 获取发送者信息 (从原始发送者，不是中继)
                    sender_user_id = self.client_to_user.get(client_id, "unknown")

                    # 转发最终消息
                    final_message['sender_user_id'] = sender_user_id
                    final_message['type'] = 'encrypted_message_received'

                    await self._send_to_client(target_client_id, json.dumps(final_message))

                    self.logger.info(f"✓ 洋葱路由最终消息已送达: {sender_user_id} -> {target_user_id}")

                except Exception as e:
                    await self._send_error(client_id, "FINAL_MESSAGE_ERROR", f"Cannot process final message: {e}")
                    return

            elif action == "forward" and next_virtual_node:
                # 转发到下一个虚拟节点 (实际上是同一个中继服务器的不同处理)
                try:
                    # 在真实的洋葱路由中，这里应该转发到不同的中继服务器
                    # 但在我们的简化实现中，我们直接处理下一层

                    # 创建转发数据包，减少一层
                    forward_packet = {
                        "type": "onion_packet",
                        "layers": layers - 1,
                        "entry_virtual_node": next_virtual_node,
                        "relay_mapping": relay_mapping,
                        "packet_data": base64.b64encode(payload_data).decode('utf-8'),
                        "timestamp": int(time.time())
                    }

                    # 递归处理下一层 (简化实现)
                    await self._handle_onion_packet(client_id, forward_packet)

                    self.logger.info(f"✓ 洋葱数据包已处理下一层: {next_virtual_node}")

                except Exception as e:
                    await self._send_error(client_id, "FORWARD_ERROR", f"Cannot process next layer: {e}")
                    return
            else:
                await self._send_error(client_id, "INVALID_ROUTING", f"Invalid action: {action}")
                return

        except Exception as e:
            self.logger.error(f"处理洋葱路由包时出错: {e}")
            await self._send_error(client_id, "ONION_ROUTING_ERROR", str(e))

    async def _handle_user_lookup(self, client_id: str, data: Dict):
        """处理用户查找请求"""
        try:
            target_user_id = data.get('target_user_id')
            query_id = data.get('query_id')  # 获取查询ID

            if not target_user_id:
                await self._send_error(client_id, "INVALID_USER_LOOKUP", "Target user ID is required")
                return

            # 检查目标用户是否在线
            is_online = target_user_id in self.anonymous_users
            target_client_id = self.anonymous_users.get(target_user_id)

            # 发送查找结果 - 使用与客户端兼容的格式
            lookup_result = {
                'type': 'user_lookup_response',  # 改为 user_lookup_response 保持一致
                'target_user_id': target_user_id,
                'is_online': is_online,          # 改为 is_online 保持一致
                'query_id': query_id,            # 添加查询ID
                'timestamp': int(time.time())
            }

            await self._send_to_client(client_id, json.dumps(lookup_result))

            self.logger.info(f"✓ 用户查找: {target_user_id} -> {'在线' if is_online else '离线'} (查询ID: {query_id})")

        except Exception as e:
            self.logger.error(f"处理用户查找时出错: {e}")
            await self._send_error(client_id, "USER_LOOKUP_ERROR", str(e))

    async def _handle_ping(self, client_id: str, data: Dict):
        """处理ping请求"""
        try:
            # 发送pong响应
            pong_response = {
                'type': 'pong',
                'timestamp': int(time.time()),
                'server_id': self.server_id
            }
            
            await self._send_to_client(client_id, json.dumps(pong_response))
            self.logger.debug(f"✓ Ping响应发送到 {client_id}")
            
        except Exception as e:
            self.logger.error(f"处理ping时出错: {e}")
            await self._send_error(client_id, "PING_ERROR", str(e))

    async def _relay_message(self, client_id: str, data: Dict, response_type: str):
        """通用消息中继"""
        target_user_id = data.get('target_user_id')
        sender_user_id = self.client_to_user.get(client_id)
        
        if not sender_user_id:
            await self._send_error(client_id, "NOT_REGISTERED", "Client not registered")
            return
        
        if not target_user_id:
            await self._send_error(client_id, "INVALID_TARGET", "Target user ID is required")
            return
        
        target_client_id = self.anonymous_users.get(target_user_id)
        if not target_client_id:
            await self._send_error(client_id, "TARGET_OFFLINE", f"User {target_user_id} is offline")
            return
        
        # 构造转发消息
        relay_message = {
            'type': response_type,
            'sender_user_id': sender_user_id,
            'timestamp': int(time.time())
        }
        relay_message.update({k: v for k, v in data.items() if k not in ['type', 'target_user_id']})
        
        # 发送到目标客户端
        await self._send_to_client(target_client_id, json.dumps(relay_message))
        
        # 发送确认给发送者
        confirmation = {
            'type': 'message_sent',
            'target_user_id': target_user_id,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(confirmation))
        
        message_desc = data.get('filename', data.get('message', 'message'))
        self.logger.info(f"✓ 消息已中继: {sender_user_id} -> {target_user_id} ({message_desc[:20]}...)")

    async def _send_to_client(self, client_id: str, message: str):
        """发送消息到客户端"""
        websocket = self.clients.get(client_id)
        if websocket:
            try:
                await websocket.send(message)
            except Exception as e:
                self.logger.error(f"发送消息到客户端 {client_id} 时出错: {e}")

    async def _send_error(self, client_id: str, error_code: str, error_message: str):
        """发送错误消息"""
        error_response = {
            'type': 'error',
            'error_code': error_code,
            'error_message': error_message,
            'timestamp': int(time.time())
        }
        await self._send_to_client(client_id, json.dumps(error_response))

    async def _cleanup_client(self, client_id: str):
        """清理客户端连接"""
        user_id = self.client_to_user.get(client_id)
        if user_id:
            del self.client_to_user[client_id]
            if user_id in self.anonymous_users:
                del self.anonymous_users[user_id]
        
        if client_id in self.clients:
            del self.clients[client_id]
        
        self.logger.info(f"客户端已断开: {client_id}")

    async def _periodic_stats(self):
        """定期统计信息"""
        while True:
            await asyncio.sleep(300)  # 5分钟
            active_connections = len(self.clients)
            self.logger.info(f"统计信息 - 活跃连接: {active_connections}")

async def main():
    """启动中继网络"""
    print("启动中继网络，端口: [8011, 8012, 8013]")
    print("✓ 启动了 3 个中继服务器")
    
    servers = []
    for i, port in enumerate([8011, 8012, 8013]):  # 使用不同的端口
        server_id = f'relay_{i+1:03d}'
        server = SimpleRelayServer(server_id, 'localhost', port)
        servers.append(server)
    
    # 启动所有服务器
    tasks = [server.start() for server in servers]
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    asyncio.run(main())
