"""
匿名通信客户端
提供端到端加密的匿名通信功能
"""

import asyncio
import json
import time
import base64
import logging
from typing import Dict, Optional, List, Set, Callable
import websockets
from ..protocol.message import Message, MessageType
from ..protocol.secure_protocol import SecureProtocol
from ..protocol.key_exchange import KeyExchange
from ..network.anonymizer import OnionRouter, TrafficObfuscator

logger = logging.getLogger(__name__)

class AnonymousClient:
    """匿名通信客户端"""
    
    def __init__(self, user_id: str, relay_servers: List[str], on_error: Optional[Callable[[str], None]] = None):
        self.user_id = user_id
        self.relay_servers = relay_servers
        self.on_error = on_error
        
        # 协议组件
        self.secure_protocol = SecureProtocol(user_id)
        self.key_exchange = KeyExchange()
        
        # 网络组件
        self.onion_router = OnionRouter()
        self.traffic_obfuscator = TrafficObfuscator()
        
        # 连接管理
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.active_circuits: Dict[str, str] = {}  # session_id -> circuit_id
        self.running = False
        self.is_registered = False
        
        # 消息处理器
        self.message_handlers: Dict[MessageType, callable] = {}
        self._setup_message_handlers()
        
        # 中继消息处理器
        self.relay_message_handlers: Dict[str, callable] = {}
        
    def _setup_message_handlers(self):
        """设置消息处理器"""
        self.message_handlers = {
            MessageType.HANDSHAKE_INIT: self._handle_handshake_init,
            MessageType.HANDSHAKE_RESPONSE: self._handle_handshake_response,
            MessageType.HANDSHAKE_COMPLETE: self._handle_handshake_complete,
            MessageType.TEXT_MESSAGE: self._handle_text_message,
            MessageType.FILE_TRANSFER_INIT: self._handle_file_transfer_init,
            MessageType.FILE_CHUNK: self._handle_file_chunk,
            MessageType.HEARTBEAT: self._handle_heartbeat,
            MessageType.ERROR: self._handle_error
        }
    
    async def start(self):
        """启动客户端"""
        try:
            self.running = True
            print(f"启动匿名客户端: {self.user_id}")
            
            # 连接到中继服务器
            await self._connect_to_relays()
            
            # 注册用户
            await self._register_with_relays()
            
            # 启动后台任务
            asyncio.create_task(self._heartbeat_loop())
            asyncio.create_task(self._connection_monitor_loop())
            
            # 开始消息处理循环
            await self._message_processing_loop()
            
        except Exception as e:
            logger.error(f"客户端启动失败: {e}")
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """停止客户端"""
        self.running = False
        await self._disconnect_from_relays()
        print("客户端已停止")
    
    async def _connect_to_relays(self):
        """连接到中继服务器"""
        for server in self.relay_servers:
            try:
                connection = await websockets.connect(server)
                self.connections[server] = connection
                print(f"✓ 已连接到中继服务器: {server}")
                
                # 启动消息接收任务
                asyncio.create_task(self._handle_relay_messages(server, connection))
                
            except Exception as e:
                logger.error(f"连接中继服务器失败: {server}, 错误: {e}")
    
    async def _disconnect_from_relays(self):
        """断开与中继服务器的连接"""
        for server in list(self.connections.keys()):
            try:
                await self.connections[server].close()
                del self.connections[server]
                print(f"✓ 已断开与中继服务器的连接: {server}")
            except Exception as e:
                logger.error(f"断开连接失败: {server}, 错误: {e}")
    
    async def _register_with_relays(self):
        """向中继服务器注册用户"""
        register_message = {
            "type": "register",
            "user_id": self.user_id,
            "timestamp": int(time.time())
        }
        
        for connection in self.connections.values():
            try:
                await connection.send(json.dumps(register_message))
                print(f"✓ 注册请求已发送")
            except Exception as e:
                logger.error(f"注册失败: {e}")
    
    async def _handle_relay_messages(self, relay_url: str, connection):
        """处理来自中继服务器的消息"""
        try:
            async for message in connection:
                try:
                    data = json.loads(message)
                    await self._process_relay_message(data)
                except json.JSONDecodeError:
                    logger.error(f"无效的JSON消息: {message}")
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"连接已关闭: {relay_url}")
        except Exception as e:
            logger.error(f"消息处理循环失败: {e}")
    
    async def _process_relay_message(self, data: Dict):
        """处理中继服务器消息"""
        message_type = data.get('type')
        
        if message_type == 'register_success':
            self.is_registered = True
            print(f"✓ 注册成功")
        elif message_type == 'protocol_message':
            # 处理协议消息
            protocol_data = data.get('data')
            if protocol_data:
                try:
                    message = self.secure_protocol.deserialize_message(protocol_data)
                    await self._handle_protocol_message(message)
                except Exception as e:
                    logger.error(f"处理协议消息失败: {e}")
        elif message_type == 'error':
            error_msg = data.get('error_message', '未知错误')
            print(f"收到中继服务器错误: {error_msg}")
            if self.on_error:
                self.on_error(error_msg)
            return
        elif message_type in self.relay_message_handlers:
            await self.relay_message_handlers[message_type](data)
        else:
            print(f"未处理的中继消息类型: {message_type}")
    
    async def _handle_protocol_message(self, message: Message):
        """处理协议消息"""
        message_type = message.header.message_type
        
        if message_type in self.message_handlers:
            await self.message_handlers[message_type](message)
        else:
            print(f"未知协议消息类型: {message_type}")
    
    async def _message_processing_loop(self):
        """消息处理循环"""
        while self.running:
            try:
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"消息处理循环失败: {e}")
    
    # 协议消息处理器
    async def _handle_handshake_init(self, message: Message):
        """处理握手初始化消息"""
        try:
            peer_id = message.header.sender_id

            # 生成密钥对
            public_key_b64 = self.key_exchange.generate_keypair(self.user_id)
            public_key_bytes = base64.b64decode(public_key_b64)

            # 执行密钥交换
            response_public_key, session_id = self.key_exchange.perform_exchange(
                self.user_id, peer_id, message.payload.content
            )
            
            # 存储会话信息
            self.secure_protocol.sessions[session_id] = {
                'peer_id': peer_id,
                'session_key': self.key_exchange.get_session_key(session_id),
                'created_at': int(time.time()),
                'last_activity': int(time.time())
            }
            
            # 创建握手响应
            response = self.secure_protocol.create_handshake_response(
                message, response_public_key, self.key_exchange.get_session_key(session_id), session_id
            )
            
            # 发送响应
            await self._send_protocol_message(response, peer_id)
            
            print(f"✓ 握手响应已发送给 {peer_id}")
            
        except Exception as e:
            logger.error(f"处理握手初始化失败: {e}")
    
    async def _handle_handshake_response(self, message: Message):
        """处理握手响应消息"""
        try:
            peer_id = message.header.sender_id

            # 解析握手响应数据
            if isinstance(message.payload.content, dict):
                # 如果content是字典，提取加密的会话密钥和会话ID
                encrypted_session_key_b64 = message.payload.content['shared_key']
                session_id = message.payload.content.get('session_id') or message.header.session_id
            else:
                # 如果content是字符串，直接使用，会话ID从header获取
                encrypted_session_key_b64 = message.payload.content
                session_id = message.header.session_id

            # 完成密钥交换
            session_id = self.key_exchange.complete_exchange(
                self.user_id, peer_id, encrypted_session_key_b64, session_id
            )
            
            # 存储会话信息
            self.secure_protocol.sessions[session_id] = {
                'peer_id': peer_id,
                'session_key': self.key_exchange.get_session_key(session_id),
                'created_at': int(time.time()),
                'last_activity': int(time.time())
            }
            
            # 创建握手完成消息
            complete = self.secure_protocol.create_handshake_complete(message)
            await self._send_protocol_message(complete, peer_id)
            
            print(f"✓ 握手完成: {peer_id}")
            
        except Exception as e:
            logger.error(f"处理握手响应失败: {e}")
    
    async def _handle_handshake_complete(self, message: Message):
        """处理握手完成消息"""
        print(f"✓ 握手完成确认来自 {message.header.sender_id}")
    
    async def _handle_text_message(self, message: Message):
        """处理文本消息"""
        try:
            decrypted_text = self.secure_protocol.decrypt_text_message(message)
            print(f"收到来自 {message.header.sender_id} 的消息: {decrypted_text}")
        except Exception as e:
            logger.error(f"处理文本消息失败: {e}")
    
    async def _handle_file_transfer_init(self, message: Message):
        """处理文件传输初始化"""
        filename = message.payload.content
        print(f"收到文件传输请求: {filename}")
    
    async def _handle_file_chunk(self, message: Message):
        """处理文件块"""
        try:
            chunk_data = self.secure_protocol.decrypt_file_chunk(message)
            print(f"收到文件块 {message.header.chunk_index + 1}/{message.header.total_chunks}")
        except Exception as e:
            logger.error(f"处理文件块失败: {e}")
    
    async def _handle_heartbeat(self, message: Message):
        """处理心跳消息"""
        session_id = message.header.session_id
        if session_id in self.secure_protocol.sessions:
            self.secure_protocol.sessions[session_id]['last_activity'] = int(time.time())
    
    async def _handle_error(self, message: Message):
        """处理错误消息"""
        error_code = message.payload.content
        print(f"收到错误: {error_code}")
    
    # 公共API
    async def add_contact(self, target_user_id: str) -> bool:
        """添加联系人并建立安全会话"""
        try:
            if not self.is_registered:
                print("用户未注册，无法添加联系人")
                return False
            
            # 生成密钥对
            public_key_b64 = self.key_exchange.generate_keypair(self.user_id)
            public_key_bytes = base64.b64decode(public_key_b64)

            # 创建握手初始化消息
            handshake = self.secure_protocol.create_handshake_init(target_user_id, public_key_bytes)
            
            # 发送握手消息
            await self._send_protocol_message(handshake, target_user_id)
            
            print(f"✓ 握手请求已发送给 {target_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加联系人失败: {e}")
            return False
    
    async def send_message(self, target_user_id: str, text: str) -> bool:
        """发送加密消息"""
        try:
            # 查找会话
            session_id = self._find_session_with_peer(target_user_id)
            if not session_id:
                # 需要先建立会话
                if not await self.add_contact(target_user_id):
                    return False
                # 等待会话建立
                await asyncio.sleep(2)
                session_id = self._find_session_with_peer(target_user_id)
            
            if not session_id:
                print(f"无法找到与 {target_user_id} 的会话")
                return False
            
            # 创建加密消息
            message = self.secure_protocol.create_text_message(target_user_id, text, session_id)
            
            # 发送消息
            await self._send_protocol_message(message, target_user_id)
            
            print(f"✓ 消息已发送给 {target_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    def _find_session_with_peer(self, peer_id: str) -> Optional[str]:
        """查找与对等方的会话"""
        for session_id, session in self.secure_protocol.sessions.items():
            if session['peer_id'] == peer_id:
                return session_id
        return None
    
    async def _send_protocol_message(self, message: Message, target_user_id: str):
        """发送协议消息"""
        try:
            # 序列化消息
            message_data = self.secure_protocol.serialize_message(message)
            
            # 创建中继消息
            relay_message = {
                "type": "protocol_message",
                "target_user_id": target_user_id,
                "data": message_data,
                "timestamp": int(time.time())
            }
            
            # 发送到中继服务器
            for connection in self.connections.values():
                try:
                    await connection.send(json.dumps(relay_message))
                    break  # 只发送到一个服务器
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    
        except Exception as e:
            logger.error(f"发送协议消息失败: {e}")
    
    # 后台任务
    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                for session_id, session in list(self.secure_protocol.sessions.items()):
                    peer_id = session['peer_id']
                    heartbeat = self.secure_protocol.create_heartbeat(peer_id, session_id)
                    await self._send_protocol_message(heartbeat, peer_id)
                
                await asyncio.sleep(30)  # 30秒心跳间隔
            except Exception as e:
                logger.error(f"心跳循环出错: {e}")
                await asyncio.sleep(5)
    
    async def _connection_monitor_loop(self):
        """连接监控循环"""
        while self.running:
            try:
                await asyncio.sleep(10)
                
                # 清理过期会话
                expired_count = self.secure_protocol.cleanup_expired_sessions()
                if expired_count > 0:
                    print(f"✓ 清理了 {expired_count} 个过期会话")
                    
            except Exception as e:
                logger.error(f"连接监控出错: {e}")
    
    def is_connected(self) -> bool:
        """检查是否已连接并注册"""
        return self.running and self.is_registered and len(self.connections) > 0

    def send_protocol_message(self, receiver_id: str, msg_type: str, payload: dict):
        from protocol.message_header import ProtocolMessage
        msg = ProtocolMessage(msg_type, self.user_id, receiver_id, payload)
        serialized = msg.serialize()
        # 发送时直接用base64字符串
        self.send(serialized)

    def on_protocol_message(self, serialized: str):
        from protocol.message_header import ProtocolMessage
        msg = ProtocolMessage.deserialize(serialized)
        # 处理msg.payload，所有密钥、消息体都为base64字符串
        # ... existing code ...
