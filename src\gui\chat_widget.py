"""
聊天组件 - 专门处理聊天界面的组件
提供消息显示、输入和格式化功能
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from datetime import datetime
from typing import Dict, List, Optional, Callable
import re


class ChatWidget:
    """聊天组件类"""
    
    def __init__(self, parent, on_send_message: Callable[[str], None] = None):
        self.parent = parent
        self.on_send_message = on_send_message
        
        # 聊天数据
        self.messages: List[Dict] = []
        self.current_contact: Optional[str] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_styles()
        
    def _create_widgets(self):
        """创建聊天界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 聊天标题
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.contact_label = ttk.Label(
            self.title_frame, 
            text="选择联系人开始聊天", 
            font=("Arial", 12, "bold")
        )
        self.contact_label.pack(side=tk.LEFT)
        
        # 在线状态指示器
        self.status_indicator = tk.Canvas(self.title_frame, width=12, height=12)
        self.status_indicator.pack(side=tk.RIGHT, padx=(5, 0))
        self._update_status_indicator(False)
        
        # 消息显示区域
        self.message_frame = ttk.Frame(self.main_frame)
        self.message_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 创建消息显示文本框
        self.message_display = scrolledtext.ScrolledText(
            self.message_frame,
            state=tk.DISABLED,
            wrap=tk.WORD,
            font=("Arial", 10),
            bg="#f8f9fa",
            relief=tk.FLAT,
            borderwidth=1
        )
        self.message_display.pack(fill=tk.BOTH, expand=True)
        
        # 消息输入区域
        self.input_frame = ttk.Frame(self.main_frame)
        self.input_frame.pack(fill=tk.X)
        
        # 输入框框架
        self.entry_frame = ttk.Frame(self.input_frame)
        self.entry_frame.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        # 消息输入框
        self.message_entry = tk.Text(
            self.entry_frame,
            height=3,
            wrap=tk.WORD,
            font=("Arial", 10),
            relief=tk.SOLID,
            borderwidth=1
        )
        self.message_entry.pack(fill=tk.BOTH, expand=True)
        
        # 按钮框架
        self.button_frame = ttk.Frame(self.input_frame)
        self.button_frame.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 发送按钮
        self.send_button = ttk.Button(
            self.button_frame,
            text="发送",
            command=self._send_message,
            state=tk.DISABLED
        )
        self.send_button.pack(fill=tk.X, pady=(0, 2))
        
        # 表情按钮
        self.emoji_button = ttk.Button(
            self.button_frame,
            text="😊",
            command=self._show_emoji_panel,
            width=3
        )
        self.emoji_button.pack(fill=tk.X)
        
        # 绑定事件
        self._setup_bindings()
    
    def _setup_styles(self):
        """设置样式"""
        # 配置消息显示区域的标签样式
        self.message_display.tag_config("sent", foreground="#0066cc", font=("Arial", 10))
        self.message_display.tag_config("received", foreground="#009900", font=("Arial", 10))
        self.message_display.tag_config("system", foreground="#666666", font=("Arial", 9, "italic"))
        self.message_display.tag_config("timestamp", foreground="#999999", font=("Arial", 8))
        self.message_display.tag_config("file", foreground="#cc6600", font=("Arial", 10, "underline"))
        self.message_display.tag_config("error", foreground="#cc0000", font=("Arial", 10))
        
        # 配置输入框样式
        self.message_entry.config(insertbackground="#333333")
    
    def _setup_bindings(self):
        """设置事件绑定"""
        # 发送消息快捷键
        self.message_entry.bind('<Control-Return>', lambda e: self._send_message())
        self.message_entry.bind('<Shift-Return>', lambda e: None)  # 允许Shift+Enter换行
        
        # 输入框内容变化事件
        self.message_entry.bind('<KeyRelease>', self._on_text_change)
        
        # 右键菜单
        self.message_entry.bind('<Button-3>', self._show_context_menu)
        self.message_display.bind('<Button-3>', self._show_display_context_menu)
    
    def _update_status_indicator(self, online: bool):
        """更新在线状态指示器"""
        self.status_indicator.delete("all")
        color = "#00cc00" if online else "#cccccc"
        self.status_indicator.create_oval(2, 2, 10, 10, fill=color, outline=color)
    
    def set_contact(self, contact_id: str, contact_name: str, online: bool = False):
        """设置当前聊天联系人"""
        self.current_contact = contact_id
        self.contact_label.config(text=f"与 {contact_name} 聊天")
        self._update_status_indicator(online)
        self.send_button.config(state=tk.NORMAL)
        self.message_entry.config(state=tk.NORMAL)
        self.message_entry.focus()
    
    def clear_contact(self):
        """清除当前联系人"""
        self.current_contact = None
        self.contact_label.config(text="选择联系人开始聊天")
        self._update_status_indicator(False)
        self.send_button.config(state=tk.DISABLED)
        self.message_entry.config(state=tk.DISABLED)
        self.clear_messages()
    
    def add_message(self, sender_id: str, content: str, timestamp: str = None, 
                   message_type: str = "text", is_sent: bool = False):
        """添加消息到聊天显示"""
        if timestamp is None:
            timestamp = datetime.now().isoformat()
        
        # 格式化时间戳
        try:
            dt = datetime.fromisoformat(timestamp)
            time_str = dt.strftime("%H:%M:%S")
        except:
            time_str = timestamp
        
        # 确定消息标签
        if is_sent:
            sender_name = "我"
            tag = "sent"
        else:
            sender_name = sender_id
            tag = "received"
        
        # 启用编辑
        self.message_display.config(state=tk.NORMAL)
        
        # 插入时间戳
        self.message_display.insert(tk.END, f"[{time_str}] ", "timestamp")
        
        # 插入发送者
        self.message_display.insert(tk.END, f"{sender_name}: ", tag)
        
        # 插入消息内容
        if message_type == "file":
            self.message_display.insert(tk.END, f"[文件] {content}", "file")
        elif message_type == "system":
            self.message_display.insert(tk.END, content, "system")
        elif message_type == "error":
            self.message_display.insert(tk.END, content, "error")
        else:
            # 处理文本消息，支持简单的格式化
            formatted_content = self._format_message_content(content)
            self.message_display.insert(tk.END, formatted_content, tag)
        
        self.message_display.insert(tk.END, "\n")
        
        # 禁用编辑并滚动到底部
        self.message_display.config(state=tk.DISABLED)
        self.message_display.see(tk.END)
        
        # 保存消息
        self.messages.append({
            'sender': sender_id,
            'content': content,
            'timestamp': timestamp,
            'type': message_type,
            'is_sent': is_sent
        })
    
    def _format_message_content(self, content: str) -> str:
        """格式化消息内容"""
        # 这里可以添加更多的格式化逻辑
        # 例如：链接检测、表情符号转换等
        
        # 简单的URL检测
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        content = re.sub(url_pattern, r'[\1]', content)
        
        return content
    
    def clear_messages(self):
        """清空消息显示"""
        self.message_display.config(state=tk.NORMAL)
        self.message_display.delete(1.0, tk.END)
        self.message_display.config(state=tk.DISABLED)
        self.messages.clear()
    
    def _send_message(self):
        """发送消息"""
        if not self.current_contact:
            return
        
        message_text = self.message_entry.get(1.0, tk.END).strip()
        if not message_text:
            return
        
        # 清空输入框
        self.message_entry.delete(1.0, tk.END)
        
        # 在界面中显示发送的消息
        self.add_message(self.current_contact, message_text, is_sent=True)
        
        # 调用回调函数
        if self.on_send_message:
            self.on_send_message(message_text)
    
    def _on_text_change(self, event):
        """文本变化事件"""
        # 可以在这里添加输入提示、字数统计等功能
        pass
    
    def _show_emoji_panel(self):
        """显示表情面板"""
        emoji_window = tk.Toplevel(self.parent)
        emoji_window.title("选择表情")
        emoji_window.geometry("300x200")
        emoji_window.resizable(False, False)
        
        # 常用表情
        emojis = [
            "😊", "😂", "😍", "🤔", "😢", "😡", "👍", "👎",
            "❤️", "💔", "🎉", "🎊", "🔥", "💯", "✨", "⭐",
            "🌟", "💫", "🌈", "🌸", "🌺", "🌻", "🌷", "🌹"
        ]
        
        # 创建表情按钮
        row = 0
        col = 0
        for emoji in emojis:
            btn = tk.Button(
                emoji_window,
                text=emoji,
                font=("Arial", 16),
                command=lambda e=emoji: self._insert_emoji(e, emoji_window),
                relief=tk.FLAT,
                width=3,
                height=1
            )
            btn.grid(row=row, column=col, padx=2, pady=2)
            
            col += 1
            if col >= 8:
                col = 0
                row += 1
    
    def _insert_emoji(self, emoji: str, window: tk.Toplevel):
        """插入表情"""
        self.message_entry.insert(tk.INSERT, emoji)
        window.destroy()
        self.message_entry.focus()
    
    def _show_context_menu(self, event):
        """显示输入框右键菜单"""
        context_menu = tk.Menu(self.parent, tearoff=0)
        context_menu.add_command(label="剪切", command=lambda: self.message_entry.event_generate("<<Cut>>"))
        context_menu.add_command(label="复制", command=lambda: self.message_entry.event_generate("<<Copy>>"))
        context_menu.add_command(label="粘贴", command=lambda: self.message_entry.event_generate("<<Paste>>"))
        context_menu.add_separator()
        context_menu.add_command(label="全选", command=lambda: self.message_entry.tag_add(tk.SEL, "1.0", tk.END))
        context_menu.add_command(label="清空", command=lambda: self.message_entry.delete(1.0, tk.END))
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def _show_display_context_menu(self, event):
        """显示消息显示区右键菜单"""
        context_menu = tk.Menu(self.parent, tearoff=0)
        context_menu.add_command(label="复制", command=lambda: self._copy_selected_text())
        context_menu.add_command(label="全选", command=lambda: self.message_display.tag_add(tk.SEL, "1.0", tk.END))
        context_menu.add_separator()
        context_menu.add_command(label="清空聊天记录", command=self._clear_chat_confirm)
        context_menu.add_command(label="保存聊天记录", command=self._save_chat_history)
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def _copy_selected_text(self):
        """复制选中的文本"""
        try:
            selected_text = self.message_display.selection_get()
            self.parent.clipboard_clear()
            self.parent.clipboard_append(selected_text)
        except tk.TclError:
            pass  # 没有选中文本
    
    def _clear_chat_confirm(self):
        """确认清空聊天记录"""
        result = messagebox.askyesno("清空聊天记录", "确定要清空当前聊天记录吗？此操作不可撤销。")
        if result:
            self.clear_messages()
    
    def _save_chat_history(self):
        """保存聊天记录"""
        from tkinter import filedialog
        
        if not self.messages:
            messagebox.showinfo("保存聊天记录", "没有聊天记录可保存")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存聊天记录",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"聊天记录 - {self.current_contact}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for msg in self.messages:
                        timestamp = msg['timestamp']
                        sender = "我" if msg['is_sent'] else msg['sender']
                        content = msg['content']
                        msg_type = msg['type']
                        
                        if msg_type == 'file':
                            f.write(f"[{timestamp}] {sender}: [文件] {content}\n")
                        else:
                            f.write(f"[{timestamp}] {sender}: {content}\n")
                
                messagebox.showinfo("保存成功", f"聊天记录已保存到: {file_path}")
                
            except Exception as e:
                messagebox.showerror("保存失败", f"保存聊天记录失败: {e}")
    
    def get_widget(self):
        """获取主组件"""
        return self.main_frame
    
    def pack(self, **kwargs):
        """打包组件"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局组件"""
        self.main_frame.grid(**kwargs)


# 示例使用
if __name__ == "__main__":
    root = tk.Tk()
    root.title("聊天组件测试")
    root.geometry("500x400")
    
    def on_send(message):
        print(f"发送消息: {message}")
        # 模拟收到回复
        chat.add_message("TestUser", f"收到: {message}", is_sent=False)
    
    chat = ChatWidget(root, on_send)
    chat.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 设置测试联系人
    chat.set_contact("TestUser", "测试用户", True)
    
    # 添加一些测试消息
    chat.add_message("TestUser", "你好！", is_sent=False)
    chat.add_message("TestUser", "这是一条测试消息", is_sent=True)
    chat.add_message("TestUser", "test.txt", message_type="file", is_sent=False)
    
    root.mainloop()
