"""
匿名化网络层
实现洋葱路由和多跳代理技术，隐藏通信双方身份
"""

import asyncio
import secrets
import json
import time
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import aiohttp
import base64
from ..crypto import SymmetricCrypto, ECDHKeyExchange, SecureHash


@dataclass
class RelayNode:
    """中继节点信息"""
    node_id: str
    address: str
    port: int
    public_key: bytes
    is_active: bool = True
    latency: float = 0.0
    last_seen: int = 0


@dataclass
class OnionLayer:
    """洋葱路由层"""
    node_id: str
    session_key: bytes
    next_hop: Optional[str] = None


class OnionRouter:
    """洋葱路由器"""
    
    def __init__(self):
        self.crypto = SymmetricCrypto()
        self.key_exchange = ECDHKeyExchange()
        self.relay_nodes: Dict[str, RelayNode] = {}
        self.circuits: Dict[str, List[OnionLayer]] = {}
        self.node_keys: Dict[str, bytes] = {}  # 节点会话密钥
    
    def add_relay_node(self, node: RelayNode) -> None:
        """添加中继节点"""
        self.relay_nodes[node.node_id] = node
    
    def remove_relay_node(self, node_id: str) -> None:
        """移除中继节点"""
        if node_id in self.relay_nodes:
            del self.relay_nodes[node_id]
        if node_id in self.node_keys:
            del self.node_keys[node_id]
    
    def get_active_nodes(self) -> List[RelayNode]:
        """获取活跃的中继节点"""
        return [node for node in self.relay_nodes.values() if node.is_active]
    
    def select_circuit_path(self, length: int = 3) -> List[str]:
        """选择电路路径"""
        active_nodes = self.get_active_nodes()
        if len(active_nodes) < length:
            raise ValueError(f"可用节点不足，需要{length}个，只有{len(active_nodes)}个")
        
        # 随机选择节点，确保不重复
        selected_nodes = secrets.SystemRandom().sample(active_nodes, length)
        return [node.node_id for node in selected_nodes]
    
    async def establish_circuit(self, circuit_id: str, path_length: int = 3) -> bool:
        """建立洋葱路由电路"""
        try:
            # 选择路径
            path = self.select_circuit_path(path_length)
            circuit_layers = []
            
            # 为每个节点建立会话密钥
            for i, node_id in enumerate(path):
                node = self.relay_nodes[node_id]
                
                # 执行密钥交换
                private_key, public_key = self.key_exchange.generate_keypair()
                
                # 这里简化处理，实际应该与节点进行真实的密钥交换
                # 生成模拟的会话密钥
                session_key = self.crypto.generate_key()
                self.node_keys[f"{circuit_id}_{node_id}"] = session_key
                
                # 创建洋葱层
                next_hop = path[i + 1] if i + 1 < len(path) else None
                layer = OnionLayer(
                    node_id=node_id,
                    session_key=session_key,
                    next_hop=next_hop
                )
                circuit_layers.append(layer)
            
            self.circuits[circuit_id] = circuit_layers
            return True
            
        except Exception as e:
            print(f"建立电路失败: {e}")
            return False
    
    def create_onion_packet(self, circuit_id: str, payload: bytes, destination: str) -> bytes:
        """创建洋葱数据包"""
        if circuit_id not in self.circuits:
            raise ValueError("电路不存在")
        
        circuit = self.circuits[circuit_id]
        packet = payload
        
        # 从最后一层开始包装
        for layer in reversed(circuit):
            # 创建路由头
            if layer.next_hop:
                routing_header = {
                    'next_hop': layer.next_hop,
                    'circuit_id': circuit_id
                }
            else:
                # 最后一跳，包含目标地址
                routing_header = {
                    'destination': destination,
                    'circuit_id': circuit_id
                }
            
            # 组合头部和载荷
            header_bytes = json.dumps(routing_header).encode('utf-8')
            combined_data = len(header_bytes).to_bytes(4, 'big') + header_bytes + packet
            
            # 使用该层的密钥加密
            session_key = self.node_keys[f"{circuit_id}_{layer.node_id}"]
            encrypted_packet, nonce = self.crypto.encrypt(combined_data, session_key)
            
            # 创建最终数据包
            packet = nonce + encrypted_packet
        
        return packet
    
    def peel_onion_layer(self, packet: bytes, node_id: str, circuit_id: str) -> Tuple[Optional[bytes], Optional[Dict]]:
        """剥离洋葱层（中继节点使用）"""
        try:
            # 获取会话密钥
            session_key = self.node_keys.get(f"{circuit_id}_{node_id}")
            if not session_key:
                return None, None
            
            # 分离nonce和密文
            nonce = packet[:self.crypto.nonce_size]
            ciphertext = packet[self.crypto.nonce_size:]
            
            # 解密
            decrypted_data = self.crypto.decrypt(ciphertext, session_key, nonce)
            
            # 解析头部
            header_length = int.from_bytes(decrypted_data[:4], 'big')
            header_bytes = decrypted_data[4:4 + header_length]
            payload = decrypted_data[4 + header_length:]
            
            routing_header = json.loads(header_bytes.decode('utf-8'))
            
            return payload, routing_header
            
        except Exception as e:
            print(f"剥离洋葱层失败: {e}")
            return None, None
    
    def destroy_circuit(self, circuit_id: str) -> None:
        """销毁电路"""
        if circuit_id in self.circuits:
            # 清理会话密钥
            for layer in self.circuits[circuit_id]:
                key_id = f"{circuit_id}_{layer.node_id}"
                if key_id in self.node_keys:
                    del self.node_keys[key_id]
            
            del self.circuits[circuit_id]


class TrafficObfuscator:
    """流量混淆器"""
    
    def __init__(self):
        self.min_packet_size = 1024
        self.max_packet_size = 8192
        self.dummy_data_ratio = 0.3  # 虚假流量比例
    
    def pad_packet(self, data: bytes) -> bytes:
        """填充数据包到固定大小"""
        target_size = secrets.randbelow(self.max_packet_size - self.min_packet_size) + self.min_packet_size
        
        if len(data) >= target_size:
            return data
        
        # 添加随机填充
        padding_size = target_size - len(data)
        padding = secrets.token_bytes(padding_size)
        
        # 添加填充标记
        padded_data = data + b'\x00' + padding_size.to_bytes(4, 'big') + padding
        return padded_data
    
    def unpad_packet(self, padded_data: bytes) -> bytes:
        """移除数据包填充"""
        try:
            # 查找填充标记
            null_index = padded_data.rfind(b'\x00')
            if null_index == -1:
                return padded_data
            
            # 检查是否是填充标记
            if null_index + 5 <= len(padded_data):
                padding_size = int.from_bytes(padded_data[null_index + 1:null_index + 5], 'big')
                if null_index + 5 + padding_size == len(padded_data):
                    return padded_data[:null_index]
            
            return padded_data
        except Exception:
            return padded_data
    
    def generate_dummy_traffic(self) -> bytes:
        """生成虚假流量"""
        size = secrets.randbelow(self.max_packet_size - self.min_packet_size) + self.min_packet_size
        dummy_data = secrets.token_bytes(size)
        
        # 添加虚假流量标记
        return b'DUMMY' + dummy_data
    
    def is_dummy_traffic(self, data: bytes) -> bool:
        """检查是否为虚假流量"""
        return data.startswith(b'DUMMY')
    
    async def add_random_delay(self, min_delay: float = 0.1, max_delay: float = 2.0) -> None:
        """添加随机延迟"""
        delay = secrets.SystemRandom().uniform(min_delay, max_delay)
        await asyncio.sleep(delay)


class AnonymousIdentityManager:
    """匿名身份管理器"""
    
    def __init__(self):
        self.identities: Dict[str, Dict] = {}
        self.active_identity: Optional[str] = None
    
    def create_identity(self, identity_id: Optional[str] = None) -> str:
        """创建匿名身份"""
        if identity_id is None:
            identity_id = self.generate_identity_id()
        
        # 生成身份密钥对
        key_exchange = ECDHKeyExchange()
        private_key, public_key = key_exchange.generate_keypair()
        
        identity = {
            'identity_id': identity_id,
            'private_key': private_key,
            'public_key': public_key,
            'created_at': int(time.time()),
            'last_used': int(time.time()),
            'usage_count': 0
        }
        
        self.identities[identity_id] = identity
        return identity_id
    
    def generate_identity_id(self) -> str:
        """生成身份ID"""
        # 使用时间戳和随机数生成唯一ID
        timestamp = int(time.time())
        random_part = secrets.token_hex(16)
        raw_id = f"{timestamp}_{random_part}"
        
        # 使用哈希生成最终ID
        hash_bytes = SecureHash.sha256(raw_id)
        return base64.b64encode(hash_bytes).decode('ascii')[:16]
    
    def switch_identity(self, identity_id: str) -> bool:
        """切换身份"""
        if identity_id in self.identities:
            self.active_identity = identity_id
            self.identities[identity_id]['last_used'] = int(time.time())
            self.identities[identity_id]['usage_count'] += 1
            return True
        return False
    
    def get_current_identity(self) -> Optional[Dict]:
        """获取当前身份"""
        if self.active_identity:
            return self.identities[self.active_identity]
        return None
    
    def rotate_identity(self) -> str:
        """轮换身份"""
        new_identity_id = self.create_identity()
        self.switch_identity(new_identity_id)
        return new_identity_id
    
    def cleanup_old_identities(self, max_age: int = 86400) -> int:
        """清理旧身份"""
        current_time = int(time.time())
        old_identities = []
        
        for identity_id, identity in self.identities.items():
            if current_time - identity['last_used'] > max_age:
                old_identities.append(identity_id)
        
        for identity_id in old_identities:
            if identity_id != self.active_identity:
                del self.identities[identity_id]
        
        return len(old_identities)


# 示例使用
if __name__ == "__main__":
    async def test_onion_routing():
        print("=== 洋葱路由测试 ===")
        
        # 创建洋葱路由器
        router = OnionRouter()
        
        # 添加中继节点
        nodes = [
            RelayNode("node1", "127.0.0.1", 8001, b"public_key_1"),
            RelayNode("node2", "127.0.0.1", 8002, b"public_key_2"),
            RelayNode("node3", "127.0.0.1", 8003, b"public_key_3"),
        ]
        
        for node in nodes:
            router.add_relay_node(node)
        
        # 建立电路
        circuit_id = "test_circuit_001"
        if await router.establish_circuit(circuit_id):
            print("✓ 电路建立成功")
            
            # 创建洋葱数据包
            message = b"Hello, anonymous world!"
            packet = router.create_onion_packet(circuit_id, message, "destination_server")
            print(f"✓ 洋葱数据包创建成功，大小: {len(packet)} 字节")
            
            # 模拟中继节点处理
            current_packet = packet
            for layer in router.circuits[circuit_id]:
                payload, header = router.peel_onion_layer(current_packet, layer.node_id, circuit_id)
                if payload and header:
                    print(f"✓ 节点 {layer.node_id} 成功剥离洋葱层")
                    current_packet = payload
                    if 'destination' in header:
                        print(f"✓ 到达目标: {header['destination']}")
                        print(f"✓ 最终载荷: {current_packet}")
                        break
            
            # 销毁电路
            router.destroy_circuit(circuit_id)
            print("✓ 电路已销毁")
        else:
            print("✗ 电路建立失败")
    
    def test_traffic_obfuscation():
        print("\n=== 流量混淆测试 ===")
        
        obfuscator = TrafficObfuscator()
        
        # 测试数据包填充
        original_data = b"This is a test message"
        padded_data = obfuscator.pad_packet(original_data)
        unpadded_data = obfuscator.unpad_packet(padded_data)
        
        print(f"原始数据长度: {len(original_data)}")
        print(f"填充后长度: {len(padded_data)}")
        print(f"去填充后长度: {len(unpadded_data)}")
        print(f"数据完整性: {original_data == unpadded_data}")
        
        # 测试虚假流量
        dummy_traffic = obfuscator.generate_dummy_traffic()
        is_dummy = obfuscator.is_dummy_traffic(dummy_traffic)
        print(f"✓ 虚假流量生成成功: {is_dummy}")
    
    def test_identity_management():
        print("\n=== 身份管理测试 ===")
        
        identity_manager = AnonymousIdentityManager()
        
        # 创建身份
        identity1 = identity_manager.create_identity()
        identity2 = identity_manager.create_identity()
        print(f"✓ 创建身份: {identity1}, {identity2}")
        
        # 切换身份
        identity_manager.switch_identity(identity1)
        current = identity_manager.get_current_identity()
        print(f"✓ 当前身份: {current['identity_id']}")
        
        # 轮换身份
        new_identity = identity_manager.rotate_identity()
        print(f"✓ 轮换到新身份: {new_identity}")
    
    # 运行测试
    asyncio.run(test_onion_routing())
    test_traffic_obfuscation()
    test_identity_management()
