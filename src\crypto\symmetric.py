"""
对称加密模块 - 使用AES-256-GCM提供高强度对称加密
支持消息和文件的加密/解密，包含完整性验证
"""

import os
import secrets
from typing import Tuple, Optional
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import <PERSON><PERSON><PERSON><PERSON>2HMAC
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
import base64


class SymmetricCrypto:
    """AES-256-GCM对称加密类"""
    
    def __init__(self):
        self.key_size = 32  # AES-256
        self.nonce_size = 12  # GCM推荐的nonce大小
        self.salt_size = 16  # PBKDF2盐值大小
        
    def generate_key(self) -> bytes:
        """生成随机的AES-256密钥"""
        return secrets.token_bytes(self.key_size)
    
    def derive_key_from_password(self, password: str, salt: Optional[bytes] = None) -> Tuple[bytes, bytes]:
        """从密码派生密钥"""
        if salt is None:
            salt = secrets.token_bytes(self.salt_size)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.key_size,
            salt=salt,
            iterations=100000,  # 高迭代次数增强安全性
        )
        key = kdf.derive(password.encode('utf-8'))
        return key, salt
    
    def derive_key_from_shared_secret(self, shared_secret: bytes, info: bytes = b"") -> bytes:
        """从共享密钥派生会话密钥"""
        hkdf = HKDF(
            algorithm=hashes.SHA256(),
            length=self.key_size,
            salt=None,
            info=info,
        )
        return hkdf.derive(shared_secret)
    
    def encrypt(self, plaintext: bytes, key: bytes, associated_data: Optional[bytes] = None) -> Tuple[bytes, bytes]:
        """
        加密数据
        返回: (密文, nonce)
        """
        if len(key) != self.key_size:
            raise ValueError(f"密钥长度必须为{self.key_size}字节")
        
        nonce = secrets.token_bytes(self.nonce_size)
        aesgcm = AESGCM(key)
        
        try:
            ciphertext = aesgcm.encrypt(nonce, plaintext, associated_data)
            return ciphertext, nonce
        except Exception as e:
            raise RuntimeError(f"加密失败: {e}")
    
    def decrypt(self, ciphertext: bytes, key: bytes, nonce: bytes, associated_data: Optional[bytes] = None) -> bytes:
        """
        解密数据
        """
        if len(key) != self.key_size:
            raise ValueError(f"密钥长度必须为{self.key_size}字节")
        
        if len(nonce) != self.nonce_size:
            raise ValueError(f"Nonce长度必须为{self.nonce_size}字节")
        
        aesgcm = AESGCM(key)
        
        try:
            plaintext = aesgcm.decrypt(nonce, ciphertext, associated_data)
            return plaintext
        except Exception as e:
            raise RuntimeError(f"解密失败: {e}")
    
    def encrypt_message(self, message: str, key: bytes, sender_id: str = "", receiver_id: str = "") -> str:
        """
        加密文本消息，返回base64编码的结果
        """
        # 使用发送者和接收者ID作为关联数据
        associated_data = f"{sender_id}:{receiver_id}".encode('utf-8') if sender_id or receiver_id else None
        
        plaintext = message.encode('utf-8')
        ciphertext, nonce = self.encrypt(plaintext, key, associated_data)
        
        # 组合nonce和密文，然后base64编码
        encrypted_data = nonce + ciphertext
        return base64.b64encode(encrypted_data).decode('ascii')
    
    def decrypt_message(self, encrypted_message: str, key: bytes, sender_id: str = "", receiver_id: str = "") -> str:
        """
        解密文本消息
        """
        try:
            # base64解码
            encrypted_data = base64.b64decode(encrypted_message.encode('ascii'))
            
            # 分离nonce和密文
            nonce = encrypted_data[:self.nonce_size]
            ciphertext = encrypted_data[self.nonce_size:]
            
            # 使用发送者和接收者ID作为关联数据
            associated_data = f"{sender_id}:{receiver_id}".encode('utf-8') if sender_id or receiver_id else None
            
            plaintext = self.decrypt(ciphertext, key, nonce, associated_data)
            return plaintext.decode('utf-8')
        except Exception as e:
            raise RuntimeError(f"消息解密失败: {e}")
    
    def encrypt_file(self, file_path: str, output_path: str, key: bytes) -> None:
        """
        加密文件
        """
        try:
            with open(file_path, 'rb') as infile:
                plaintext = infile.read()
            
            # 使用文件名作为关联数据
            filename = os.path.basename(file_path)
            associated_data = filename.encode('utf-8')
            
            ciphertext, nonce = self.encrypt(plaintext, key, associated_data)
            
            # 保存nonce和密文到输出文件
            with open(output_path, 'wb') as outfile:
                outfile.write(nonce)
                outfile.write(ciphertext)
                
        except Exception as e:
            raise RuntimeError(f"文件加密失败: {e}")
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str, key: bytes, original_filename: str) -> None:
        """
        解密文件
        """
        try:
            with open(encrypted_file_path, 'rb') as infile:
                nonce = infile.read(self.nonce_size)
                ciphertext = infile.read()
            
            # 使用原始文件名作为关联数据
            associated_data = original_filename.encode('utf-8')
            
            plaintext = self.decrypt(ciphertext, key, nonce, associated_data)
            
            with open(output_path, 'wb') as outfile:
                outfile.write(plaintext)
                
        except Exception as e:
            raise RuntimeError(f"文件解密失败: {e}")


# 工具函数
def secure_delete(data: bytes) -> None:
    """安全删除内存中的敏感数据"""
    if isinstance(data, bytes):
        # Python中无法直接覆盖内存，但可以创建新的随机数据覆盖变量
        random_data = secrets.token_bytes(len(data))
        data = random_data
        del random_data


# 示例使用
if __name__ == "__main__":
    crypto = SymmetricCrypto()
    
    # 生成密钥
    key = crypto.generate_key()
    print(f"生成的密钥: {base64.b64encode(key).decode()}")
    
    # 加密消息
    message = "这是一条秘密消息"
    encrypted = crypto.encrypt_message(message, key, "Alice", "Bob")
    print(f"加密后: {encrypted}")
    
    # 解密消息
    decrypted = crypto.decrypt_message(encrypted, key, "Alice", "Bob")
    print(f"解密后: {decrypted}")
    
    # 从密码派生密钥
    password = "my_secure_password"
    derived_key, salt = crypto.derive_key_from_password(password)
    print(f"派生密钥: {base64.b64encode(derived_key).decode()}")
    print(f"盐值: {base64.b64encode(salt).decode()}")
