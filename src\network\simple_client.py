"""
简化的客户端 - 专门用于GUI界面
处理基本的连接、注册和消息传递
"""

import asyncio
import json
import time
import websockets
from typing import List, Dict, Optional, Callable


class SimpleAnonymousClient:
    """简化的匿名客户端 - 专门用于GUI"""
    
    def __init__(self, user_id: str, relay_servers: List[str]):
        self.user_id = user_id
        self.relay_servers = relay_servers
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.is_registered = False
        self.running = False
        
        # 消息处理回调
        self.message_handlers: Dict[str, Callable] = {}
        
    def set_message_handler(self, message_type: str, handler: Callable):
        """设置消息处理器"""
        self.message_handlers[message_type] = handler
    
    async def start(self):
        """启动客户端"""
        print(f"启动简化客户端，用户ID: {self.user_id}")
        self.running = True
        
        # 连接到中继服务器
        await self._connect_to_relays()
        
        # 注册用户
        await self._register_user()
        
        # 启动消息接收任务
        tasks = []
        for relay_url, connection in self.connections.items():
            task = asyncio.create_task(self._handle_relay_messages(relay_url, connection))
            tasks.append(task)
        
        # 等待所有任务完成
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e:
                print(f"客户端任务出错: {e}")
    
    async def stop(self):
        """停止客户端"""
        print("停止简化客户端")
        self.running = False
        self.is_registered = False
        
        # 关闭所有连接
        for connection in self.connections.values():
            try:
                await connection.close()
            except:
                pass
        self.connections.clear()
    
    async def _connect_to_relays(self):
        """连接到中继服务器"""
        for relay_url in self.relay_servers:
            try:
                print(f"连接到中继服务器: {relay_url}")
                connection = await websockets.connect(relay_url)
                self.connections[relay_url] = connection
                print(f"✓ 已连接到中继服务器: {relay_url}")
            except Exception as e:
                print(f"✗ 连接中继服务器失败 {relay_url}: {e}")
    
    async def _register_user(self):
        """注册用户"""
        if not self.connections:
            raise Exception("没有可用的中继服务器连接")
        
        # 向第一个可用的服务器发送注册消息
        for relay_url, connection in self.connections.items():
            try:
                register_message = {
                    "type": "register",
                    "user_id": self.user_id,
                    "display_name": self.user_id,
                    "timestamp": int(time.time())
                }
                
                await connection.send(json.dumps(register_message))
                print(f"✓ 已向 {relay_url} 发送注册消息")
                
                # 等待注册响应
                response = await asyncio.wait_for(connection.recv(), timeout=5.0)
                response_data = json.loads(response)
                
                if response_data.get('type') == 'register_success':
                    self.is_registered = True
                    print(f"✓ 用户注册成功: {self.user_id}")
                    break
                else:
                    print(f"注册失败: {response_data}")
                    
            except asyncio.TimeoutError:
                print(f"注册超时: {relay_url}")
            except Exception as e:
                print(f"注册失败 {relay_url}: {e}")
        
        if not self.is_registered:
            raise Exception("用户注册失败")
    
    async def _handle_relay_messages(self, relay_url: str, connection):
        """处理中继服务器消息"""
        try:
            async for message in connection:
                if not self.running:
                    break
                    
                try:
                    data = json.loads(message)
                    await self._process_message(data)
                except json.JSONDecodeError:
                    print(f"收到无效JSON消息: {message}")
                except Exception as e:
                    print(f"处理消息时出错: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print(f"与中继服务器 {relay_url} 的连接已断开")
            if relay_url in self.connections:
                del self.connections[relay_url]
        except Exception as e:
            print(f"处理中继消息时出错: {e}")
    
    async def _process_message(self, data: Dict):
        """处理接收到的消息"""
        message_type = data.get('type')
        
        if message_type in self.message_handlers:
            try:
                await self.message_handlers[message_type](data)
            except Exception as e:
                print(f"消息处理器出错 {message_type}: {e}")
        else:
            print(f"未处理的消息类型: {message_type}")
    
    async def send_message(self, target_user_id: str, message: str):
        """发送消息"""
        if not self.is_registered:
            raise Exception("用户未注册")
        
        if not self.connections:
            raise Exception("没有可用的连接")
        
        # 选择第一个可用连接发送
        for connection in self.connections.values():
            try:
                message_data = {
                    "type": "encrypted_message",
                    "target_user_id": target_user_id,
                    "encrypted_content": message,  # 简化版本，实际应该加密
                    "timestamp": int(time.time())
                }
                
                await connection.send(json.dumps(message_data))
                print(f"✓ 消息已发送到 {target_user_id}")
                return True
                
            except Exception as e:
                print(f"发送消息失败: {e}")
        
        return False
    
    async def send_file(self, target_user_id: str, filename: str, file_data: bytes):
        """发送文件"""
        if not self.is_registered:
            raise Exception("用户未注册")
        
        if not self.connections:
            raise Exception("没有可用的连接")
        
        # 选择第一个可用连接发送
        for connection in self.connections.values():
            try:
                import base64
                
                file_message = {
                    "type": "encrypted_file",
                    "target_user_id": target_user_id,
                    "filename": filename,
                    "file_data": base64.b64encode(file_data).decode('utf-8'),
                    "file_size": len(file_data),
                    "timestamp": int(time.time())
                }
                
                await connection.send(json.dumps(file_message))
                print(f"✓ 文件已发送到 {target_user_id}: {filename}")
                return True
                
            except Exception as e:
                print(f"发送文件失败: {e}")
        
        return False
    
    async def lookup_user(self, target_user_id: str) -> bool:
        """查找用户是否在线"""
        if not self.is_registered:
            return False
        
        if not self.connections:
            return False
        
        # 选择第一个可用连接查询
        for connection in self.connections.values():
            try:
                lookup_message = {
                    "type": "user_lookup",
                    "target_user_id": target_user_id,
                    "timestamp": int(time.time())
                }
                
                await connection.send(json.dumps(lookup_message))
                return True
                
            except Exception as e:
                print(f"用户查找失败: {e}")
        
        return False


# 兼容性适配器
class AnonymousClient(SimpleAnonymousClient):
    """兼容性适配器 - 保持与原有代码的兼容性"""

    def __init__(self, user_id: str, relay_servers: List[str], on_error: Callable = None, on_contact_request: Callable = None):
        super().__init__(user_id, relay_servers)
        self.on_error = on_error
        self.on_contact_request = on_contact_request
        print(f"创建兼容性客户端: {user_id}")

    async def add_contact(self, target_user_id: str) -> bool:
        """添加联系人 - 简化版本，直接返回成功"""
        try:
            # 简化版本：直接发送用户查找请求
            return await self.lookup_user(target_user_id)
        except Exception as e:
            if self.on_error:
                self.on_error(f"添加联系人失败: {e}")
            return False

    async def lookup_user_status(self, target_user_id: str) -> bool:
        """查询用户状态 - 兼容性方法"""
        return await self.lookup_user(target_user_id)
